#!/usr/bin/env node
/**
 * Test script to verify X-ray routes are working
 */

const express = require('express');
const path = require('path');

// Test if the xrayRoutes module can be loaded
try {
  console.log('🧪 Testing X-ray routes module...');
  
  // Try to require the xrayRoutes module
  const xrayRoutes = require('./routes/xrayRoutes');
  console.log('✅ X-ray routes module loaded successfully');
  
  // Check if it's a valid Express router
  if (typeof xrayRoutes === 'function') {
    console.log('✅ X-ray routes is a valid Express router');
  } else {
    console.log('❌ X-ray routes is not a valid Express router');
    console.log('Type:', typeof xrayRoutes);
  }
  
  // Create a test Express app
  const app = express();
  app.use('/api/xray', xrayRoutes);
  
  // Test if the routes are registered
  const routes = [];
  app._router.stack.forEach(function(middleware) {
    if (middleware.route) {
      routes.push({
        method: Object.keys(middleware.route.methods)[0].toUpperCase(),
        path: middleware.route.path
      });
    } else if (middleware.name === 'router') {
      middleware.handle.stack.forEach(function(handler) {
        if (handler.route) {
          routes.push({
            method: Object.keys(handler.route.methods)[0].toUpperCase(),
            path: '/api/xray' + handler.route.path
          });
        }
      });
    }
  });
  
  console.log('📋 Registered routes:');
  routes.forEach(route => {
    console.log(`  ${route.method} ${route.path}`);
  });
  
  // Check if required files exist
  const requiredFiles = [
    'xray.pt',
    'ai_enhance.py',
    'yolo_detect.py'
  ];
  
  console.log('\n📁 Checking required files:');
  requiredFiles.forEach(file => {
    const fs = require('fs');
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${file} exists`);
    } else {
      console.log(`  ❌ ${file} missing`);
    }
  });
  
  console.log('\n🎉 X-ray routes test completed successfully!');
  
} catch (error) {
  console.error('❌ Error testing X-ray routes:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
