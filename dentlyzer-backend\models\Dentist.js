const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Sub-schema for time slots
const TimeSlotSchema = new mongoose.Schema({
  date: { type: Date, required: true },
  time: { type: String, required: true }, // e.g., "14:30"
  isAvailable: { type: Boolean, default: true },
  duration: { type: Number, required: true }, // in minutes, inherited from dentist.slotDuration
}, { _id: false });

// Sub-schema for bilingual strings
const BilingualStringSchema = new mongoose.Schema({
  en: { type: String, required: true },
  ar: { type: String, required: true },
}, { _id: false });

// Sub-schema for bilingual services
const DentistryServiceSchema = new mongoose.Schema({
  en: { type: String, required: true },
  ar: { type: String, required: true },
}, { _id: false });

// Main Dentist schema
const DentistSchema = new mongoose.Schema({
  dentistId: { type: String, required: true, unique: true }, // Unique ID like 'D001'
  // Email at the root level, but not unique anymore
  email: { type: String, required: true },
  password: { type: String }, // Added password field
  plainPassword: { type: String }, // Store the unhashed password
  name: BilingualStringSchema,
  clinicName: BilingualStringSchema,
  about: BilingualStringSchema,
  services: [DentistryServiceSchema],
  address: {
    street: BilingualStringSchema,
    city: BilingualStringSchema,
    country: BilingualStringSchema,
    postalCode: { type: String },
  },
  contactInfo: {
    phone: { type: String, required: true },
    email: { type: String, required: true },
    website: { type: String },
  },
  image: { type: String },
  logo: { type: String },
  mapUrl: { type: String },
  workingHours: {
    monday: { type: String, default: '09:00-17:00' },
    tuesday: { type: String, default: '09:00-17:00' },
    wednesday: { type: String, default: '09:00-17:00' },
    thursday: { type: String, default: '09:00-17:00' },
    friday: { type: String, default: 'Closed' },
    saturday: { type: String, default: '09:00-13:00' },
    sunday: { type: String, default: 'Closed' },
  },
  holidays: [{ type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] }],
  timeSlots: [TimeSlotSchema],
  slotDuration: { type: Number, default: 60 }, // in minutes, default to 60
  slotBeginDate: { type: Date, required: true }, // Slots beginning date
  slotEndDate: { type: Date, required: true }, // Slots ending date
}, { timestamps: true });

// Hash password before saving
DentistSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  try {
    if (this.password) {
      // Store the plain password
      this.plainPassword = this.password;

      // Hash the password
      this.password = await bcrypt.hash(this.password, 10);
    }
    next();
  } catch (error) {
    next(error);
  }
});

module.exports = mongoose.model('Dentist', DentistSchema);