const mongoose = require('mongoose');

const ProcedureRequestSchema = new mongoose.Schema({
  studentId: {
    type: String,
    required: true
  },
  studentName: {
    type: String,
    required: true
  },
  procedureType: {
    type: String,
    enum: [
      'Periodontics',
      'Endodontics',
      'Oral Surgery',
      'Fixed Prosthodontics',
      'Removable Prosthodontics',
      'Operative'
    ],
    required: true
  },
  patientNationalId: {
    type: String,
    default: ''
  },
  patientName: {
    type: String,
    default: ''
  },
  notes: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  requestDate: {
    type: Date,
    default: Date.now
  },
  responseDate: {
    type: Date
  },
  responseNotes: {
    type: String,
    default: ''
  },
  responderId: {
    type: String
  },
  responderName: {
    type: String
  }
}, { timestamps: true });

module.exports = mongoose.model('ProcedureRequest', ProcedureRequestSchema);
