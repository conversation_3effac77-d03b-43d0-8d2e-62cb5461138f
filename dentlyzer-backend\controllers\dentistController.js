const Dentist = require('../models/Dentist');

// Helper function to generate time slots
const generateTimeSlots = (startDateStr, endDateStr, availableTimes, duration, workingHours, holidays = []) => {
  const startDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);
  const timeSlots = [];

  // Map day of week to working hours key
  const dayMap = {
    0: 'sunday',
    1: 'monday',
    2: 'tuesday',
    3: 'wednesday',
    4: 'thursday',
    5: 'friday',
    6: 'saturday'
  };

  // Loop through each day from start date to end date
  for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
    const dayOfWeek = date.getDay();
    const dayName = dayMap[dayOfWeek];

    // Skip days that are holidays or have 'Closed' working hours
    if (holidays.includes(dayName) || workingHours[dayName] === 'Closed') {
      continue;
    }

    // For each available time slot
    availableTimes.forEach(time => {
      timeSlots.push({
        date: new Date(date),
        time: time,
        isAvailable: true,
        duration: duration
      });
    });
  }

  return timeSlots;
};

exports.getAllDentists = async (req, res) => {
  try {
    const dentists = await Dentist.find().select(
      'name dentistId address clinicName logo image services about'
    );
    console.log('Returning dentists:', dentists);
    res.status(200).json(dentists);
  } catch (error) {
    console.error('Error fetching dentists:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.getDentistById = async (req, res) => {
  try {
    const dentistId = req.params.id;
    console.log('Fetching dentist with dentistId:', dentistId);

    if (!dentistId || dentistId === 'undefined') {
      console.warn('Invalid dentistId received:', dentistId);
      return res.status(400).json({ message: 'Invalid dentist ID' });
    }

    const dentist = await Dentist.findOne({ dentistId }).select(
      'name dentistId address contactInfo timeSlots clinicName logo image ' +
      'services about workingHours slotBeginDate slotEndDate holidays'
    );

    if (!dentist) {
      console.log('Dentist not found for dentistId:', dentistId);
      return res.status(404).json({ message: 'Dentist not found' });
    }

    res.status(200).json(dentist);
  } catch (error) {
    console.error('Error fetching dentist:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.createDentist = async (req, res) => {
  try {
    const {
      dentistId,
      name,
      clinicName,
      about,
      services,
      address,
      contactInfo,
      slotBeginDate,
      slotEndDate,
      slotDuration,
      holidays,
      workingHours,
      availableSlots,
    } = req.body;

    // Validate required fields
    if (!dentistId || !name || !clinicName || !address || !contactInfo || !slotBeginDate || !slotEndDate || !slotDuration || !availableSlots) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate contact info fields
    if (!contactInfo.email || contactInfo.email.trim() === '') {
      return res.status(400).json({ message: 'Email is required in contact information' });
    }

    if (!contactInfo.phone) {
      return res.status(400).json({ message: 'Phone is required in contact information' });
    }

    // Check if dentistId is unique
    const existingDentist = await Dentist.findOne({ dentistId });
    if (existingDentist) {
      return res.status(400).json({ message: 'Dentist ID already exists' });
    }

    // Email uniqueness check removed - email is no longer required to be unique

    // Ensure all required bilingual fields have values
    const defaultBilingualValue = { en: 'Not provided', ar: 'غير متوفر' };

    // Create a deep copy of the data to avoid modifying the request object directly
    const processedAbout = about && about.en && about.ar ? about : defaultBilingualValue;

    // Process services
    const processedServices = Array.isArray(services) && services.length > 0
      ? services.map(service => {
          if (service && service.en && service.ar) {
            return service;
          }
          return { en: 'General Dentistry', ar: 'طب الأسنان العام' };
        })
      : [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];

    // Ensure address fields have values
    const processedAddress = {
      street: address.street && address.street.en && address.street.ar ? address.street : defaultBilingualValue,
      city: address.city && address.city.en && address.city.ar ? address.city : defaultBilingualValue,
      country: address.country && address.country.en && address.country.ar ? address.country : defaultBilingualValue,
      postalCode: address.postalCode || '',
    };

    // Process contact info to ensure no null values
    const processedContactInfo = {
      email: contactInfo.email.trim(),
      phone: contactInfo.phone,
      website: contactInfo.website || '',
    };

    // Generate time slots based on the provided parameters
    const timeSlots = generateTimeSlots(slotBeginDate, slotEndDate, availableSlots, slotDuration, workingHours, holidays);

    const dentist = new Dentist({
      dentistId,
      // Add email at the root level to match the schema
      email: contactInfo.email.trim(),
      name,
      clinicName,
      about: processedAbout,
      services: processedServices,
      address: processedAddress,
      contactInfo: processedContactInfo,
      slotBeginDate,
      slotEndDate,
      slotDuration,
      holidays,
      workingHours,
      timeSlots,
    });

    await dentist.save();
    res.status(201).json(dentist);
  } catch (error) {
    console.error('Error creating dentist:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

exports.updateDentist = async (req, res) => {
  try {
    const dentistId = req.params.id;
    const updates = { ...req.body };

    // Validate contact info fields if they are being updated
    if (updates.contactInfo) {
      if (!updates.contactInfo.email || updates.contactInfo.email.trim() === '') {
        return res.status(400).json({ message: 'Email is required in contact information' });
      }

      if (!updates.contactInfo.phone) {
        return res.status(400).json({ message: 'Phone is required in contact information' });
      }

      // Email uniqueness check removed - email is no longer required to be unique
      if (updates.contactInfo.email) {
        // Update the root level email field
        updates.email = updates.contactInfo.email.trim();
      }

      // Process contact info to ensure no null values
      updates.contactInfo = {
        email: updates.contactInfo.email.trim(),
        phone: updates.contactInfo.phone,
        website: updates.contactInfo.website || '',
      };
    }

    // Ensure all required bilingual fields have values if they are being updated
    const defaultBilingualValue = { en: 'Not provided', ar: 'غير متوفر' };

    if (updates.about && (!updates.about.en || !updates.about.ar)) {
      updates.about = defaultBilingualValue;
    }

    // Process services if they are being updated
    if (updates.services) {
      updates.services = Array.isArray(updates.services) && updates.services.length > 0
        ? updates.services.map(service => {
            if (service && service.en && service.ar) {
              return service;
            }
            return { en: 'General Dentistry', ar: 'طب الأسنان العام' };
          })
        : [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];
    }

    // Process address fields if they are being updated
    if (updates.address) {
      if (updates.address.street && (!updates.address.street.en || !updates.address.street.ar)) {
        updates.address.street = defaultBilingualValue;
      }

      if (updates.address.city && (!updates.address.city.en || !updates.address.city.ar)) {
        updates.address.city = defaultBilingualValue;
      }

      if (updates.address.country && (!updates.address.country.en || !updates.address.country.ar)) {
        updates.address.country = defaultBilingualValue;
      }
    }

    // Check if slot settings are being updated
    if (updates.slotBeginDate && updates.slotEndDate && updates.availableSlots && updates.slotDuration) {
      // Regenerate time slots based on the new parameters
      updates.timeSlots = generateTimeSlots(
        updates.slotBeginDate,
        updates.slotEndDate,
        updates.availableSlots,
        updates.slotDuration,
        updates.workingHours || {},
        updates.holidays || []
      );

      console.log(`Regenerated ${updates.timeSlots.length} time slots with duration ${updates.slotDuration} minutes`);
    }

    const dentist = await Dentist.findOneAndUpdate(
      { dentistId },
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!dentist) {
      return res.status(404).json({ message: 'Dentist not found' });
    }

    res.status(200).json(dentist);
  } catch (error) {
    console.error('Error updating dentist:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete a dentist
exports.deleteDentist = async (req, res) => {
  try {
    const dentistId = req.params.id;
    const dentist = await Dentist.findOneAndDelete({ dentistId });

    if (!dentist) {
      return res.status(404).json({ message: 'Dentist not found' });
    }

    res.status(200).json({ message: 'Dentist deleted successfully' });
  } catch (error) {
    console.error('Error deleting dentist:', error);
    res.status(500).json({ message: 'Server error' });
  }
};