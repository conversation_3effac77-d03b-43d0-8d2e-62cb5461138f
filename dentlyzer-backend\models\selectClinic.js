const mongoose = require('mongoose');
const Joi = require('joi');
const Patient = require('../models/Patient');

// Clinic Selection Schema
const clinicSelectionSchema = new mongoose.Schema({
  nationalId: { type: String, required: true, index: true },
  studentId: { type: String, required: true },
  clinic: {
    type: String,
    enum: [
      'Operative',
      'Fixed Prosthodontics',
      'Removable Prosthodontics',
      'Endodontics',
      'Periodontics',
      'Oral Surgery',
    ],
    required: true,
  },
  uniqueId: { type: String, required: true, unique: true },
  date: { type: Date, required: true },
  sheetData: { type: Object, default: {} }, // Store form data for clinic-specific sheets
  toothChartId: { type: mongoose.Schema.Types.ObjectId, ref: 'TeethChart' }, // Reference to a tooth chart
});

const ClinicSelection = mongoose.model('ClinicSelection', clinicSelectionSchema);

// Joi Validation Schema for Clinic Selection
const selectionSchema = Joi.object({
  nationalId: Joi.string().required(),
  studentId: Joi.string().required(),
  clinic: Joi.string()
    .valid(
      'Operative',
      'Fixed Prosthodontics',
      'Removable Prosthodontics',
      'Endodontics',
      'Periodontics',
      'Oral Surgery'
    )
    .required(),
  uniqueId: Joi.string().required(),
  date: Joi.date().required(),
});

// Joi Validation Schema for Sheet Data
const sheetDataSchema = Joi.object().unknown(true); // Allow any fields for flexibility

// Create Clinic Selection
const createClinicSelection = async (req, res) => {
  const { error, value } = selectionSchema.validate(req.body, {
    abortEarly: false,
  });

  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map((d) => d.message),
    });
  }

  try {
    // Verify patient exists
    const patient = await Patient.findOne({ nationalId: value.nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Check if uniqueId is already used
    const existingSelection = await ClinicSelection.findOne({ uniqueId: value.uniqueId });
    if (existingSelection) {
      return res.status(400).json({ message: 'Unique ID already exists' });
    }

    // Create new clinic selection
    const selection = new ClinicSelection({
      nationalId: value.nationalId,
      studentId: value.studentId,
      clinic: value.clinic,
      uniqueId: value.uniqueId,
      date: value.date,
    });

    await selection.save();

    // Update patient's clinic selections
    patient.clinicSelections = patient.clinicSelections || [];
    patient.clinicSelections.push(selection._id);
    await patient.save();

    res.status(201).json({
      message: 'Clinic selection created successfully',
      selection,
    });
  } catch (error) {
    console.error('Error creating clinic selection:', error);
    res.status(500).json({
      message: 'Server error while creating clinic selection',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Get Clinic Selections for a Patient
const getClinicSelections = async (req, res) => {
  try {
    const selections = await ClinicSelection.find({ nationalId: req.params.nationalId })
      .populate('toothChartId')
      .lean();
    res.json(selections);
  } catch (error) {
    console.error('Error fetching clinic selections:', error);
    res.status(500).json({
      message: 'Server error while fetching clinic selections',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Save Sheet Data
const saveSheetData = async (req, res) => {
  const { error, value } = sheetDataSchema.validate(req.body.sheetData, {
    abortEarly: false,
  });

  if (error) {
    return res.status(400).json({
      message: 'Validation error in sheet data',
      errors: error.details.map((d) => d.message),
    });
  }

  try {
    const selection = await ClinicSelection.findOne({ uniqueId: req.params.uniqueId });
    if (!selection) {
      return res.status(404).json({ message: 'Clinic selection not found' });
    }

    selection.sheetData = value;
    await selection.save();

    res.json({
      message: 'Sheet data saved successfully',
      selection,
    });
  } catch (error) {
    console.error('Error saving sheet data:', error);
    res.status(500).json({
      message: 'Server error while saving sheet data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

module.exports = {
  createClinicSelection,
  getClinicSelections,
  saveSheetData,
};