const mongoose = require('mongoose');

const ActivityLogSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, required: true },
  userName: { type: String, required: true },
  userRole: { type: String, enum: ['superadmin', 'admin', 'supervisor', 'student', 'dentist', 'assistant', 'patient'], required: true },
  action: { type: String, required: true }, // e.g., "Created Student", "Edited Patient"
  details: { type: String }, // Additional details about the action
  ipAddress: { type: String },
  timestamp: { type: Date, default: Date.now },
  // Legacy fields for backward compatibility
  performedBy: { type: mongoose.Schema.Types.ObjectId, refPath: 'performedByModel' },
  performedByModel: { type: String, enum: ['Superadmin', 'Admin', 'Supervisor', 'Student', 'Dentist', 'Assistant', 'Patient'] },
}, { timestamps: true });

module.exports = mongoose.model('ActivityLog', ActivityLogSchema);