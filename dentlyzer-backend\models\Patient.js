// Updated Patient Model (Patient.js)
const mongoose = require('mongoose');

const consentSchema = new mongoose.Schema({
  signatureText: { type: String, default: '' },
  signatureImage: { type: String, default: '' }, // Base64 encoded image
  signedAt: { type: Date },
  isSigned: { type: Boolean, default: false }
});

const sheetSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics']
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  details: {
    diagnosis: { type: String, required: true },
    treatmentPlan: { type: String, required: true },
    notes: { type: String, default: '' },
    specificData: { type: mongoose.Schema.Types.Mixed, default: {} }
  }
});

const patientSchema = new mongoose.Schema({
  nationalId: { type: String, required: true, unique: true, index: true },
  fullName: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  gender: { type: String, required: true },
  age: { type: Number, required: true },
  address: { type: String, required: true },
  occupation: { type: String, required: true },
  // Authentication fields
  email: { type: String, required: true, unique: true, index: true },
  password: { type: String, required: true },
  role: { type: String, default: 'patient' },
  // Medical information
  medicalInfo: {
    chronicDiseases: { type: [String], default: [] },
    recentSurgicalProcedures: { type: String, default: '' },
    currentMedications: { type: String, default: '' },
    chiefComplaint: { type: String, default: '' },
  },
  drId: { type: String, required: true },
  consent: { type: consentSchema, default: () => ({}) },
  xrays: [{ url: String, date: { type: Date, default: Date.now }, note: String }],
  galleryImages: [{ url: String, date: { type: Date, default: Date.now }, note: String }],
  appointments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Appointment' }],
  teethCharts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'TeethChart' }],
  treatmentSheets: [sheetSchema]
});

module.exports = mongoose.model('Patient', patientSchema);