const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const StudentSchema = new mongoose.Schema({
  studentId: { type: String, required: true, unique: true }, // Added studentId field
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  plainPassword: { type: String }, // Store the unhashed password
  name: { type: String, required: true },
  role: { type: String, default: 'student' },
  patients: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Patient' }],
  reviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Review' }],
  appointments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Appointment' }], // Added for assigned appointments
  university: { type: String }, // Required for patient-initiated appointments
}, { timestamps: true });

// Hash password before saving
StudentSchema.pre('save', async function (next) {
  console.log('Student pre-save hook triggered');
  if (!this.isModified('password')) {
    console.log('Password not modified, skipping hashing');
    return next();
  }
  console.log('Hashing password in Student model pre-save hook');
  console.log('Original password (first 3 chars):', this.password.substring(0, 3) + '***');

  // Store the plain password
  this.plainPassword = this.password;

  // Hash the password
  this.password = await bcrypt.hash(this.password, 10);
  console.log('Hashed password (first 20 chars):', this.password.substring(0, 20) + '...');
  next();
});

module.exports = mongoose.model('Student', StudentSchema);