#!/usr/bin/env node
/**
 * Test script to verify X-ray endpoints are working
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5000';

async function testEndpoints() {
  console.log('🧪 Testing X-ray endpoints...\n');
  
  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/api/xray/health`);
    console.log('✅ Health check passed');
    console.log('   Status:', healthResponse.data.status);
    console.log('   Model exists:', healthResponse.data.modelExists);
    console.log('   Model path:', healthResponse.data.modelPath);
    
    // Test 2: Check if we have a test image
    console.log('\n2️⃣ Checking for test images...');
    const testImagePath = path.join(__dirname, 'uploads', 'xray1.jpg');
    
    if (!fs.existsSync(testImagePath)) {
      console.log('❌ Test image not found at:', testImagePath);
      console.log('   Available images in uploads:');
      const uploadsDir = path.join(__dirname, 'uploads');
      if (fs.existsSync(uploadsDir)) {
        const files = fs.readdirSync(uploadsDir).filter(f => f.match(/\.(jpg|jpeg|png)$/i));
        files.forEach(file => console.log(`     - ${file}`));
        
        if (files.length > 0) {
          const firstImage = path.join(uploadsDir, files[0]);
          console.log(`   Using first available image: ${files[0]}`);
          await testWithImage(firstImage, files[0]);
        } else {
          console.log('   No test images available. Skipping image tests.');
        }
      }
    } else {
      console.log('✅ Test image found');
      await testWithImage(testImagePath, 'xray1.jpg');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

async function testWithImage(imagePath, imageName) {
  try {
    console.log(`\n3️⃣ Testing enhancement endpoint with ${imageName}...`);
    
    // Test enhancement endpoint
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));
    
    const enhanceResponse = await axios.post(`${BASE_URL}/api/xray/enhance`, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      timeout: 30000 // 30 second timeout
    });
    
    console.log('✅ Enhancement endpoint working');
    console.log('   Success:', enhanceResponse.data.success);
    console.log('   Message:', enhanceResponse.data.message);
    console.log('   Enhanced image size:', enhanceResponse.data.enhancedImage ? enhanceResponse.data.enhancedImage.length : 'N/A');
    
    console.log(`\n4️⃣ Testing analysis endpoint with ${imageName}...`);
    
    // Test analysis endpoint
    const formData2 = new FormData();
    formData2.append('image', fs.createReadStream(imagePath));
    
    const analyzeResponse = await axios.post(`${BASE_URL}/api/xray/analyze`, formData2, {
      headers: {
        ...formData2.getHeaders(),
      },
      timeout: 30000 // 30 second timeout
    });
    
    console.log('✅ Analysis endpoint working');
    console.log('   Success:', analyzeResponse.data.success);
    console.log('   Results count:', analyzeResponse.data.results ? analyzeResponse.data.results.length : 0);
    console.log('   Model used:', analyzeResponse.data.model);
    
    if (analyzeResponse.data.results && analyzeResponse.data.results.length > 0) {
      console.log('   Detected conditions:');
      analyzeResponse.data.results.forEach((result, idx) => {
        console.log(`     ${idx + 1}. ${result.class} (${(result.confidence * 100).toFixed(1)}%)`);
      });
    } else {
      console.log('   No conditions detected');
    }
    
  } catch (error) {
    console.error('❌ Image test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the tests
testEndpoints().then(() => {
  console.log('\n🎉 Endpoint testing completed!');
}).catch(error => {
  console.error('\n💥 Test suite failed:', error.message);
  process.exit(1);
});
