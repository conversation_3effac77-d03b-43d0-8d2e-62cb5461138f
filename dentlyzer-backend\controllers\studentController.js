const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const Student = require('../models/Student');

// Get all patients for a student
const getMyPatients = async (req, res) => {
  try {
    console.log('Getting patients for student:', req.user);

    // Use studentId instead of _id
    const patients = await Patient.find({ drId: req.user.studentId });

    console.log(`Found ${patients.length} patients for student ${req.user.studentId}`);
    res.json(patients);
  } catch (error) {
    console.error('Error getting patients:', error);
    res.status(500).json({ message: error.message });
  }
};

// Messaging (Socket.io integration - basic emit)
const sendMessage = (io) => async (req, res) => {
  const { receiverId, message } = req.body;
  try {
    io.to(receiverId).emit('message', { sender: req.user.id, message });
    res.json({ message: 'Message sent' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Analytics (e.g., patient count, completed appointments)
const getAnalytics = async (req, res) => {
  try {
    console.log('Getting analytics for student:', req.user.studentId);

    const patientCount = await Patient.countDocuments({ drId: req.user.studentId });
    const completedAppointments = await Appointment.countDocuments({
      doctor: req.user.studentId,
      doctorModel: 'Student',
      status: 'completed',
    });

    console.log(`Analytics for student ${req.user.studentId}: ${patientCount} patients, ${completedAppointments} completed appointments`);
    res.json({ patientCount, completedAppointments });
  } catch (error) {
    console.error('Error getting analytics:', error);
    res.status(500).json({ message: error.message });
  }
};


const getAllStudents = async (req, res) => {
  try {
    const students = await Student.find({ role: 'student' })
      .select('name studentId') // Return studentId instead of _id
      .lean();

    if (!students || students.length === 0) {
      return res.status(404).json({ message: 'No students found' });
    }

    res.json(students);
  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({
      message: 'Server error while fetching students',
      error: error.message
    });
  }
};


module.exports = { getMyPatients, sendMessage, getAnalytics, getAllStudents };