const mongoose = require('mongoose');

const appointmentSchema = mongoose.Schema(
  {
    date: { type: Date, required: true },
    time: { type: String, required: true },
    type: { type: String, required: true },
    notes: { type: String, default: '' },
    status: {
      type: String,
      enum: ['pending', 'completed', 'cancelled'],
      default: 'pending',
    },
    patient: { type: mongoose.Schema.Types.ObjectId, ref: 'Patient' }, // Optional for non-existing patients
    doctor: { type: String }, // Store studentId as String
    doctorModel: { type: String, enum: ['Student', 'Dentist'] },
    chiefComplaint: { type: String, required: true },
    isPatientInitiated: { type: Boolean, default: false },
    treatment: { type: String, default: '' },
    duration: { type: Number, default: 120 },
    university: { type: String },
    fullName: { type: String },
    phoneNumber: { type: String },
    age: { type: Number },
    nationalId: { type: String }, // Added nationalId field
    occupation: { type: String, default: '' },
    address: { type: String, default: '' },
    studentName: { type: String }, // Student name for display
    studentId: { type: String },   // Student ID for reference
  },
  { timestamps: true }
);

module.exports = mongoose.model('Appointment', appointmentSchema);