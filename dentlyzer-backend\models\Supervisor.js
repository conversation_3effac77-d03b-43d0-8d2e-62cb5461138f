const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const SupervisorSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  plainPassword: { type: String }, // Store the unhashed password
  name: { type: String, required: true },
  role: { type: String, default: 'supervisor' },
  pendingReviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Review' }],
  completedReviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Review' }],
  university: { type: String }, // Required for patient-initiated appointments
  signature: { type: String, default: null }, // Store supervisor's signature
}, { timestamps: true, collection: 'supervisors' });

// Hash password before saving
SupervisorSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  // Store the plain password
  this.plainPassword = this.password;

  // Hash the password
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

module.exports = mongoose.model('Supervisor', SupervisorSchema);