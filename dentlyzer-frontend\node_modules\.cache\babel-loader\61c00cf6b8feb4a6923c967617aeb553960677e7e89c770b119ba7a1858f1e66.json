{"ast": null, "code": "var _jsxFileName = \"D:\\\\New folder (3)\\\\dentlyzer-frontend\\\\src\\\\dentist\\\\XRay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport DentistSidebar from './DentistSidebar';\nimport Navbar from './Navbar';\nimport { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot, FaDownload, FaImage, FaEye, FaFileImage } from 'react-icons/fa';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XRay = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [aiResult, setAiResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [enhancing, setEnhancing] = useState(false);\n  const [error, setError] = useState('');\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    setEnhancedImageUrl(null);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setPreviewUrl(reader.result);\n      reader.readAsDataURL(file);\n    } else {\n      setPreviewUrl(null);\n    }\n  };\n  const handleAnalyze = async () => {\n    if (!selectedFile) return;\n    setLoading(true);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.results) {\n        setAiResult({\n          findings: response.data.results.map(r => ({\n            label: r.class,\n            confidence: r.confidence\n          })),\n          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`\n        });\n        if (response.data.annotatedImagePath) {\n          // Extract filename from the full path (handle both forward and back slashes)\n          const filename = response.data.annotatedImagePath.split(/[/\\\\]/).pop();\n          const annotatedUrl = `${process.env.REACT_APP_API_URL}/api/xray/annotated-image/${filename}`;\n          console.log('Setting annotated image URL:', annotatedUrl);\n          setAnnotatedImageUrl(annotatedUrl);\n        }\n      } else {\n        setError('No results from AI analysis.');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Failed to analyze X-ray image.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnhance = async () => {\n    if (!selectedFile) return;\n    setEnhancing(true);\n    setError('');\n    setEnhancedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.enhancedImage) {\n        setEnhancedImageUrl(response.data.enhancedImage);\n      } else {\n        setError('No enhanced image received from server.');\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || 'Failed to enhance X-ray image.');\n    } finally {\n      setEnhancing(false);\n    }\n  };\n  const generatePDFReport = async () => {\n    if (!aiResult || !aiResult.findings) {\n      alert('No analysis results available to generate PDF report.');\n      return;\n    }\n    try {\n      const pdf = new jsPDF();\n      const pageWidth = pdf.internal.pageSize.width;\n      const pageHeight = pdf.internal.pageSize.height;\n      let yPosition = 20;\n\n      // Header\n      pdf.setFontSize(20);\n      pdf.setTextColor(0, 100, 200);\n      pdf.text('Dentlyzer X-Ray Analysis Report', pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 15;\n\n      // Date and time\n      pdf.setFontSize(12);\n      pdf.setTextColor(100, 100, 100);\n      const currentDate = new Date().toLocaleString();\n      pdf.text(`Generated on: ${currentDate}`, pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 20;\n\n      // AI Model Information\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('AI Model Information', 20, yPosition);\n      yPosition += 10;\n      pdf.setFontSize(10);\n      pdf.text('• Model: YOLOv8 trained on dental X-ray dataset', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Capabilities: Detects 31 different dental conditions', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Conditions include: Caries, Crown, Filling, Implant, Missing teeth, etc.', 25, yPosition);\n      yPosition += 15;\n\n      // Analysis Results\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('Analysis Results', 20, yPosition);\n      yPosition += 10;\n      if (aiResult.findings.length > 0) {\n        // Create table data for detected conditions\n        const tableData = aiResult.findings.map((finding, index) => [(index + 1).toString(), finding.label, `${(finding.confidence * 100).toFixed(1)}%`, finding.confidence >= 0.7 ? 'High' : finding.confidence >= 0.5 ? 'Medium' : 'Low']);\n\n        // Add table\n        pdf.autoTable({\n          startY: yPosition,\n          head: [['#', 'Condition', 'Confidence', 'Reliability']],\n          body: tableData,\n          theme: 'grid',\n          headStyles: {\n            fillColor: [0, 100, 200],\n            textColor: 255\n          },\n          alternateRowStyles: {\n            fillColor: [245, 245, 245]\n          },\n          margin: {\n            left: 20,\n            right: 20\n          },\n          styles: {\n            fontSize: 10\n          }\n        });\n        yPosition = pdf.lastAutoTable.finalY + 15;\n\n        // Summary\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 100, 200);\n        pdf.text('Summary:', 20, yPosition);\n        yPosition += 8;\n        pdf.setFontSize(10);\n        pdf.setTextColor(0, 0, 0);\n        const summaryText = aiResult.summary || `AI detected ${aiResult.findings.length} condition(s) in this X-ray image.`;\n        const splitSummary = pdf.splitTextToSize(summaryText, pageWidth - 40);\n        pdf.text(splitSummary, 20, yPosition);\n        yPosition += splitSummary.length * 6 + 10;\n      } else {\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 150, 0);\n        pdf.text('✓ No significant dental pathology detected in this X-ray image.', 20, yPosition);\n        yPosition += 15;\n      }\n\n      // Add images if available\n      if (previewUrl) {\n        // Check if we have space for images\n        if (yPosition + 80 > pageHeight - 20) {\n          pdf.addPage();\n          yPosition = 20;\n        }\n        pdf.setFontSize(14);\n        pdf.setTextColor(0, 0, 0);\n        pdf.text('X-Ray Images', 20, yPosition);\n        yPosition += 15;\n        try {\n          // Add original image\n          const imgWidth = 70;\n          const imgHeight = 50;\n          pdf.setFontSize(10);\n          pdf.text('Original X-Ray:', 20, yPosition);\n\n          // Convert base64 image to add to PDF\n          pdf.addImage(previewUrl, 'JPEG', 20, yPosition + 5, imgWidth, imgHeight);\n\n          // Add enhanced image if available\n          if (enhancedImageUrl) {\n            pdf.text('Enhanced X-Ray:', 110, yPosition);\n            pdf.addImage(enhancedImageUrl, 'PNG', 110, yPosition + 5, imgWidth, imgHeight);\n          }\n          yPosition += imgHeight + 15;\n        } catch (imgError) {\n          console.warn('Could not add images to PDF:', imgError);\n          pdf.setFontSize(10);\n          pdf.setTextColor(150, 150, 150);\n          pdf.text('(Images could not be included in this report)', 20, yPosition);\n          yPosition += 10;\n        }\n      }\n\n      // Footer\n      if (yPosition + 30 > pageHeight - 20) {\n        pdf.addPage();\n        yPosition = pageHeight - 40;\n      } else {\n        yPosition = pageHeight - 40;\n      }\n      pdf.setFontSize(8);\n      pdf.setTextColor(100, 100, 100);\n      pdf.text('This report was generated by Dentlyzer AI system.', pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      pdf.text('For medical decisions, please consult with a qualified dental professional.', pageWidth / 2, yPosition + 8, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      const fileName = `Dentlyzer_XRay_Analysis_${new Date().toISOString().split('T')[0]}.pdf`;\n      pdf.save(fileName);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Failed to generate PDF report. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(DentistSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"X-Ray Analysis & Enhancement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"AI-powered dental X-ray analysis with advanced enhancement capabilities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n                  className: \"text-[#0077B6]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"YOLOv8 AI Model\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.label, {\n                  className: \"flex flex-col items-center cursor-pointer group\",\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-6 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6] group-hover:bg-[rgba(0,119,182,0.15)] transition-all duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaUpload, {\n                      className: \"text-4xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-[#0077B6] font-semibold text-lg mt-4 mb-2\",\n                    children: \"Upload X-Ray Image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"Click to select or drag and drop your X-ray image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"image/*\",\n                    className: \"hidden\",\n                    onChange: handleFileChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), previewUrl && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.2\n                  },\n                  className: \"mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative inline-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: previewUrl,\n                      alt: \"X-Ray Preview\",\n                      className: \"w-full max-w-sm rounded-xl border-2 border-[rgba(0,119,182,0.2)] shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-2 right-2 bg-[#0077B6] text-white px-2 py-1 rounded-lg text-xs font-medium\",\n                      children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                        className: \"inline mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 27\n                      }, this), \"Original\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this), selectedFile && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.3\n                  },\n                  className: \"flex flex-col sm:flex-row gap-4 mt-8 justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: \"bg-gradient-to-r from-[#20B2AA] to-[#48CAE4] text-white px-8 py-3 rounded-full font-semibold flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300\",\n                    onClick: handleEnhance,\n                    disabled: !selectedFile || enhancing,\n                    children: [/*#__PURE__*/_jsxDEV(FaMagic, {\n                      className: \"text-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 25\n                    }, this), enhancing ? 'Enhancing...' : 'Enhance X-Ray']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: \"bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-3 rounded-full font-semibold flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300\",\n                    onClick: handleAnalyze,\n                    disabled: !selectedFile || loading,\n                    children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n                      className: \"text-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 25\n                    }, this), loading ? 'Analyzing...' : 'AI Analysis']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: -20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: \"mt-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                  className: \"h-5 w-5 text-red-500 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-red-700 font-medium\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this), enhancedImageUrl && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.2\n              },\n              className: \"mt-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(32,178,170,0.2)] p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-bold text-[#20B2AA] flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaMagic, {\n                    className: \"mr-3 text-2xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this), \"Enhanced X-Ray Results\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Enhancement Complete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-gray-700 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaFileImage, {\n                        className: \"mr-2 text-[#0077B6]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 376,\n                        columnNumber: 27\n                      }, this), \"Original Image\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs bg-gray-100 px-2 py-1 rounded-full\",\n                      children: \"Before\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: previewUrl,\n                      alt: \"Original X-Ray\",\n                      className: \"w-full rounded-xl border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-[#20B2AA] flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaMagic, {\n                        className: \"mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 27\n                      }, this), \"Enhanced Image\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs bg-[rgba(32,178,170,0.1)] text-[#20B2AA] px-2 py-1 rounded-full\",\n                      children: \"After\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: enhancedImageUrl,\n                      alt: \"Enhanced X-Ray\",\n                      className: \"w-full rounded-xl border-2 border-[rgba(32,178,170,0.3)] shadow-lg hover:shadow-xl transition-all duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-2 right-2 bg-[#20B2AA] text-white px-2 py-1 rounded-lg text-xs font-medium\",\n                      children: [/*#__PURE__*/_jsxDEV(FaMagic, {\n                        className: \"inline mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 27\n                      }, this), \"Enhanced\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 p-4 bg-[rgba(32,178,170,0.05)] rounded-lg border border-[rgba(32,178,170,0.2)]\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"text-[#20B2AA] text-xl mt-0.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-[#20B2AA] mb-1\",\n                      children: \"Enhancement Applied Successfully\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-700 text-sm\",\n                      children: \"Your X-ray image has been enhanced using advanced AI algorithms including noise reduction, contrast enhancement, edge sharpening, and brightness optimization for better visibility of dental structures.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), aiResult && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.3\n              },\n              className: \"mt-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.2)] p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6] flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n                      className: \"mr-3 text-2xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 437,\n                      columnNumber: 25\n                    }, this), \"AI Analysis Results\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 text-sm mt-1\",\n                    children: \"Powered by YOLOv8 dental AI model\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: generatePDFReport,\n                  className: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-full font-semibold flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300\",\n                  title: \"Download comprehensive analysis report as PDF\",\n                  children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n                    className: \"text-lg\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 23\n                  }, this), \"Download PDF Report\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6 p-4 bg-[rgba(0,119,182,0.05)] rounded-xl border border-[rgba(0,119,182,0.1)]\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaRobot, {\n                      className: \"text-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-semibold text-[#0077B6]\",\n                      children: \"YOLOv8 Dental AI Model\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"Advanced deep learning for dental diagnostics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-700 leading-relaxed\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Detection Capabilities:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 23\n                  }, this), \" Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth, Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures, Orthodontic brackets, and 18+ other dental conditions with high accuracy.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), aiResult.findings.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-[#0077B6] mb-3 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 27\n                    }, this), \"Detected Conditions (\", aiResult.findings.length, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid gap-3\",\n                    children: aiResult.findings.map((f, idx) => {\n                      const confidenceLevel = f.confidence >= 0.8 ? 'high' : f.confidence >= 0.6 ? 'medium' : 'low';\n                      const confidenceColor = confidenceLevel === 'high' ? 'text-green-700 bg-green-100' : confidenceLevel === 'medium' ? 'text-yellow-700 bg-yellow-100' : 'text-orange-700 bg-orange-100';\n                      const borderColor = confidenceLevel === 'high' ? 'border-green-200' : confidenceLevel === 'medium' ? 'border-yellow-200' : 'border-orange-200';\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        initial: {\n                          opacity: 0,\n                          x: -20\n                        },\n                        animate: {\n                          opacity: 1,\n                          x: 0\n                        },\n                        transition: {\n                          delay: idx * 0.1\n                        },\n                        className: `bg-white p-4 rounded-xl border-2 ${borderColor} shadow-sm hover:shadow-md transition-all duration-300`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-8 h-8 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6] font-bold text-sm\",\n                              children: idx + 1\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 496,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"font-semibold text-gray-800\",\n                              children: f.label\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 499,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 495,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `text-xs px-3 py-1 rounded-full font-medium ${confidenceColor}`,\n                              children: confidenceLevel.toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 502,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-sm font-bold text-[#0077B6] bg-[rgba(0,119,182,0.1)] px-3 py-1 rounded-full\",\n                              children: [(f.confidence * 100).toFixed(1), \"%\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 505,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 501,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 494,\n                          columnNumber: 33\n                        }, this)\n                      }, idx, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4 bg-[rgba(0,119,182,0.05)] rounded-xl border border-[rgba(0,119,182,0.1)]\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                      className: \"text-[#0077B6] text-xl mt-0.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-[#0077B6] mb-1\",\n                        children: \"Analysis Summary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-700 text-sm\",\n                        children: aiResult.summary\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 521,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 bg-green-50 rounded-xl border-2 border-green-200 text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 rounded-full bg-green-100 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                      className: \"text-green-600 text-2xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-green-800 mb-1\",\n                      children: \"Excellent News!\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-green-700 text-sm\",\n                      children: \"No significant dental pathology detected in this X-ray image.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this), annotatedImageUrl && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.4\n                },\n                className: \"mt-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-[#0077B6] flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 27\n                    }, this), \"Annotated X-Ray Image\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs bg-[rgba(0,119,182,0.1)] text-[#0077B6] px-2 py-1 rounded-full\",\n                    children: \"AI Annotations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: annotatedImageUrl,\n                    alt: \"Annotated X-Ray\",\n                    className: \"w-full max-w-2xl mx-auto rounded-xl border-2 border-[rgba(0,119,182,0.3)] shadow-lg hover:shadow-xl transition-all duration-300\",\n                    onError: e => {\n                      console.error('Failed to load annotated image:', annotatedImageUrl);\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'block';\n                    },\n                    onLoad: () => {\n                      console.log('Annotated image loaded successfully:', annotatedImageUrl);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute top-2 right-2 bg-[#0077B6] text-white px-2 py-1 rounded-lg text-xs font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n                      className: \"inline mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 27\n                    }, this), \"AI Detected\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-4 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n                    style: {\n                      display: 'none'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                        className: \"h-5 w-5 text-red-500 mr-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 580,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-red-700 font-medium\",\n                          children: \"Could not load annotated image\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 582,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-red-600 text-sm\",\n                          children: \"The analysis was successful, but the annotated image is not available.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 583,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 581,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-[rgba(0,119,182,0.05)] rounded-lg border border-[rgba(0,119,182,0.1)]\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"inline mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 27\n                    }, this), \"Bounding boxes and labels show detected dental conditions with confidence scores\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n_s(XRay, \"LggJ7dspVJn4gPaqFJYM5cPlyBU=\");\n_c = XRay;\nexport default XRay;\nvar _c;\n$RefreshReg$(_c, \"XRay\");", "map": {"version": 3, "names": ["React", "useState", "DentistSidebar", "<PERSON><PERSON><PERSON>", "FaUpload", "FaSearch", "FaCheckCircle", "FaExclamationTriangle", "FaMagic", "FaRobot", "FaDownload", "FaImage", "FaEye", "FaFileImage", "motion", "axios", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XRay", "_s", "sidebarOpen", "setSidebarOpen", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "aiResult", "setAiResult", "loading", "setLoading", "enhancing", "setEnhancing", "error", "setError", "annotatedImageUrl", "setAnnotatedImageUrl", "enhancedImageUrl", "setEnhancedImageUrl", "handleFileChange", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleAnalyze", "formData", "FormData", "append", "response", "post", "process", "env", "REACT_APP_API_URL", "headers", "data", "results", "findings", "map", "r", "label", "class", "confidence", "summary", "join", "annotatedImagePath", "filename", "split", "pop", "annotatedUrl", "console", "log", "err", "_err$response", "_err$response$data", "handleEnhance", "enhancedImage", "_err$response2", "_err$response2$data", "generatePDFReport", "alert", "pdf", "pageWidth", "internal", "pageSize", "width", "pageHeight", "height", "yPosition", "setFontSize", "setTextColor", "text", "align", "currentDate", "Date", "toLocaleString", "length", "tableData", "finding", "index", "toString", "toFixed", "autoTable", "startY", "head", "body", "theme", "headStyles", "fillColor", "textColor", "alternateRowStyles", "margin", "left", "right", "styles", "fontSize", "lastAutoTable", "finalY", "summaryText", "splitSummary", "splitTextToSize", "addPage", "imgWidth", "imgHeight", "addImage", "imgError", "warn", "fileName", "toISOString", "save", "className", "children", "isOpen", "setIsOpen", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "opacity", "animate", "transition", "duration", "y", "delay", "whileHover", "scale", "whileTap", "type", "accept", "onChange", "src", "alt", "button", "onClick", "disabled", "title", "f", "idx", "confidenceLevel", "confidenceColor", "borderColor", "x", "toUpperCase", "onError", "style", "display", "nextS<PERSON>ling", "onLoad", "_c", "$RefreshReg$"], "sources": ["D:/New folder (3)/dentlyzer-frontend/src/dentist/XRay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport DentistSidebar from './DentistSidebar';\nimport Navbar from './Navbar';\nimport { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot, FaDownload, FaImage, FaEye, FaFileImage } from 'react-icons/fa';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\n\nconst XRay = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [aiResult, setAiResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [enhancing, setEnhancing] = useState(false);\n  const [error, setError] = useState('');\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    setEnhancedImageUrl(null);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setPreviewUrl(reader.result);\n      reader.readAsDataURL(file);\n    } else {\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleAnalyze = async () => {\n    if (!selectedFile) return;\n    setLoading(true);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' },\n      });\n      if (response.data && response.data.results) {\n        setAiResult({\n          findings: response.data.results.map(r => ({ label: r.class, confidence: r.confidence })),\n          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`\n        });\n        if (response.data.annotatedImagePath) {\n          // Extract filename from the full path (handle both forward and back slashes)\n          const filename = response.data.annotatedImagePath.split(/[/\\\\]/).pop();\n          const annotatedUrl = `${process.env.REACT_APP_API_URL}/api/xray/annotated-image/${filename}`;\n          console.log('Setting annotated image URL:', annotatedUrl);\n          setAnnotatedImageUrl(annotatedUrl);\n        }\n      } else {\n        setError('No results from AI analysis.');\n      }\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to analyze X-ray image.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEnhance = async () => {\n    if (!selectedFile) return;\n    setEnhancing(true);\n    setError('');\n    setEnhancedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' },\n      });\n      if (response.data && response.data.enhancedImage) {\n        setEnhancedImageUrl(response.data.enhancedImage);\n      } else {\n        setError('No enhanced image received from server.');\n      }\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to enhance X-ray image.');\n    } finally {\n      setEnhancing(false);\n    }\n  };\n\n  const generatePDFReport = async () => {\n    if (!aiResult || !aiResult.findings) {\n      alert('No analysis results available to generate PDF report.');\n      return;\n    }\n\n    try {\n      const pdf = new jsPDF();\n      const pageWidth = pdf.internal.pageSize.width;\n      const pageHeight = pdf.internal.pageSize.height;\n      let yPosition = 20;\n\n      // Header\n      pdf.setFontSize(20);\n      pdf.setTextColor(0, 100, 200);\n      pdf.text('Dentlyzer X-Ray Analysis Report', pageWidth / 2, yPosition, { align: 'center' });\n      yPosition += 15;\n\n      // Date and time\n      pdf.setFontSize(12);\n      pdf.setTextColor(100, 100, 100);\n      const currentDate = new Date().toLocaleString();\n      pdf.text(`Generated on: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' });\n      yPosition += 20;\n\n      // AI Model Information\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('AI Model Information', 20, yPosition);\n      yPosition += 10;\n\n      pdf.setFontSize(10);\n      pdf.text('• Model: YOLOv8 trained on dental X-ray dataset', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Capabilities: Detects 31 different dental conditions', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Conditions include: Caries, Crown, Filling, Implant, Missing teeth, etc.', 25, yPosition);\n      yPosition += 15;\n\n      // Analysis Results\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('Analysis Results', 20, yPosition);\n      yPosition += 10;\n\n      if (aiResult.findings.length > 0) {\n        // Create table data for detected conditions\n        const tableData = aiResult.findings.map((finding, index) => [\n          (index + 1).toString(),\n          finding.label,\n          `${(finding.confidence * 100).toFixed(1)}%`,\n          finding.confidence >= 0.7 ? 'High' : finding.confidence >= 0.5 ? 'Medium' : 'Low'\n        ]);\n\n        // Add table\n        pdf.autoTable({\n          startY: yPosition,\n          head: [['#', 'Condition', 'Confidence', 'Reliability']],\n          body: tableData,\n          theme: 'grid',\n          headStyles: { fillColor: [0, 100, 200], textColor: 255 },\n          alternateRowStyles: { fillColor: [245, 245, 245] },\n          margin: { left: 20, right: 20 },\n          styles: { fontSize: 10 }\n        });\n\n        yPosition = pdf.lastAutoTable.finalY + 15;\n\n        // Summary\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 100, 200);\n        pdf.text('Summary:', 20, yPosition);\n        yPosition += 8;\n\n        pdf.setFontSize(10);\n        pdf.setTextColor(0, 0, 0);\n        const summaryText = aiResult.summary || `AI detected ${aiResult.findings.length} condition(s) in this X-ray image.`;\n        const splitSummary = pdf.splitTextToSize(summaryText, pageWidth - 40);\n        pdf.text(splitSummary, 20, yPosition);\n        yPosition += splitSummary.length * 6 + 10;\n\n      } else {\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 150, 0);\n        pdf.text('✓ No significant dental pathology detected in this X-ray image.', 20, yPosition);\n        yPosition += 15;\n      }\n\n      // Add images if available\n      if (previewUrl) {\n        // Check if we have space for images\n        if (yPosition + 80 > pageHeight - 20) {\n          pdf.addPage();\n          yPosition = 20;\n        }\n\n        pdf.setFontSize(14);\n        pdf.setTextColor(0, 0, 0);\n        pdf.text('X-Ray Images', 20, yPosition);\n        yPosition += 15;\n\n        try {\n          // Add original image\n          const imgWidth = 70;\n          const imgHeight = 50;\n\n          pdf.setFontSize(10);\n          pdf.text('Original X-Ray:', 20, yPosition);\n\n          // Convert base64 image to add to PDF\n          pdf.addImage(previewUrl, 'JPEG', 20, yPosition + 5, imgWidth, imgHeight);\n\n          // Add enhanced image if available\n          if (enhancedImageUrl) {\n            pdf.text('Enhanced X-Ray:', 110, yPosition);\n            pdf.addImage(enhancedImageUrl, 'PNG', 110, yPosition + 5, imgWidth, imgHeight);\n          }\n\n          yPosition += imgHeight + 15;\n        } catch (imgError) {\n          console.warn('Could not add images to PDF:', imgError);\n          pdf.setFontSize(10);\n          pdf.setTextColor(150, 150, 150);\n          pdf.text('(Images could not be included in this report)', 20, yPosition);\n          yPosition += 10;\n        }\n      }\n\n      // Footer\n      if (yPosition + 30 > pageHeight - 20) {\n        pdf.addPage();\n        yPosition = pageHeight - 40;\n      } else {\n        yPosition = pageHeight - 40;\n      }\n\n      pdf.setFontSize(8);\n      pdf.setTextColor(100, 100, 100);\n      pdf.text('This report was generated by Dentlyzer AI system.', pageWidth / 2, yPosition, { align: 'center' });\n      pdf.text('For medical decisions, please consult with a qualified dental professional.', pageWidth / 2, yPosition + 8, { align: 'center' });\n\n      // Save the PDF\n      const fileName = `Dentlyzer_XRay_Analysis_${new Date().toISOString().split('T')[0]}.pdf`;\n      pdf.save(fileName);\n\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Failed to generate PDF report. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <DentistSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\n                    X-Ray Analysis & Enhancement\n                  </h1>\n                  <p className=\"text-[#333333]\">AI-powered dental X-ray analysis with advanced enhancement capabilities</p>\n                </div>\n                <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                  <FaRobot className=\"text-[#0077B6]\" />\n                  <span>YOLOv8 AI Model</span>\n                </div>\n              </div>\n              {/* Upload Section */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 }}\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-8\"\n              >\n                <div className=\"text-center\">\n                  <motion.label\n                    className=\"flex flex-col items-center cursor-pointer group\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <div className=\"p-6 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6] group-hover:bg-[rgba(0,119,182,0.15)] transition-all duration-300\">\n                      <FaUpload className=\"text-4xl\" />\n                    </div>\n                    <span className=\"text-[#0077B6] font-semibold text-lg mt-4 mb-2\">Upload X-Ray Image</span>\n                    <span className=\"text-gray-500 text-sm\">Click to select or drag and drop your X-ray image</span>\n                    <input type=\"file\" accept=\"image/*\" className=\"hidden\" onChange={handleFileChange} />\n                  </motion.label>\n\n                  {previewUrl && (\n                    <motion.div\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 0.2 }}\n                      className=\"mt-6\"\n                    >\n                      <div className=\"relative inline-block\">\n                        <img\n                          src={previewUrl}\n                          alt=\"X-Ray Preview\"\n                          className=\"w-full max-w-sm rounded-xl border-2 border-[rgba(0,119,182,0.2)] shadow-lg\"\n                        />\n                        <div className=\"absolute top-2 right-2 bg-[#0077B6] text-white px-2 py-1 rounded-lg text-xs font-medium\">\n                          <FaImage className=\"inline mr-1\" />\n                          Original\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  {selectedFile && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.3 }}\n                      className=\"flex flex-col sm:flex-row gap-4 mt-8 justify-center\"\n                    >\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        className=\"bg-gradient-to-r from-[#20B2AA] to-[#48CAE4] text-white px-8 py-3 rounded-full font-semibold flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300\"\n                        onClick={handleEnhance}\n                        disabled={!selectedFile || enhancing}\n                      >\n                        <FaMagic className=\"text-lg\" />\n                        {enhancing ? 'Enhancing...' : 'Enhance X-Ray'}\n                      </motion.button>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        className=\"bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-3 rounded-full font-semibold flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300\"\n                        onClick={handleAnalyze}\n                        disabled={!selectedFile || loading}\n                      >\n                        <FaRobot className=\"text-lg\" />\n                        {loading ? 'Analyzing...' : 'AI Analysis'}\n                      </motion.button>\n                    </motion.div>\n                  )}\n                </div>\n              </motion.div>\n              {error && (\n                <motion.div\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"mt-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n                >\n                  <div className=\"flex items-center\">\n                    <FaExclamationTriangle className=\"h-5 w-5 text-red-500 mr-3\" />\n                    <p className=\"text-red-700 font-medium\">{error}</p>\n                  </div>\n                </motion.div>\n              )}\n              {enhancedImageUrl && (\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.2 }}\n                  className=\"mt-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(32,178,170,0.2)] p-6\"\n                >\n                  <div className=\"flex justify-between items-center mb-6\">\n                    <h2 className=\"text-xl font-bold text-[#20B2AA] flex items-center\">\n                      <FaMagic className=\"mr-3 text-2xl\" />\n                      Enhanced X-Ray Results\n                    </h2>\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                      <span>Enhancement Complete</span>\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center justify-between\">\n                        <h3 className=\"font-semibold text-gray-700 flex items-center\">\n                          <FaFileImage className=\"mr-2 text-[#0077B6]\" />\n                          Original Image\n                        </h3>\n                        <span className=\"text-xs bg-gray-100 px-2 py-1 rounded-full\">Before</span>\n                      </div>\n                      <div className=\"relative\">\n                        <img\n                          src={previewUrl}\n                          alt=\"Original X-Ray\"\n                          className=\"w-full rounded-xl border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center justify-between\">\n                        <h3 className=\"font-semibold text-[#20B2AA] flex items-center\">\n                          <FaMagic className=\"mr-2\" />\n                          Enhanced Image\n                        </h3>\n                        <span className=\"text-xs bg-[rgba(32,178,170,0.1)] text-[#20B2AA] px-2 py-1 rounded-full\">After</span>\n                      </div>\n                      <div className=\"relative\">\n                        <img\n                          src={enhancedImageUrl}\n                          alt=\"Enhanced X-Ray\"\n                          className=\"w-full rounded-xl border-2 border-[rgba(32,178,170,0.3)] shadow-lg hover:shadow-xl transition-all duration-300\"\n                        />\n                        <div className=\"absolute top-2 right-2 bg-[#20B2AA] text-white px-2 py-1 rounded-lg text-xs font-medium\">\n                          <FaMagic className=\"inline mr-1\" />\n                          Enhanced\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-6 p-4 bg-[rgba(32,178,170,0.05)] rounded-lg border border-[rgba(32,178,170,0.2)]\">\n                    <div className=\"flex items-start space-x-3\">\n                      <FaCheckCircle className=\"text-[#20B2AA] text-xl mt-0.5\" />\n                      <div>\n                        <h4 className=\"font-semibold text-[#20B2AA] mb-1\">Enhancement Applied Successfully</h4>\n                        <p className=\"text-gray-700 text-sm\">\n                          Your X-ray image has been enhanced using advanced AI algorithms including noise reduction,\n                          contrast enhancement, edge sharpening, and brightness optimization for better visibility\n                          of dental structures.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n              {aiResult && (\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3 }}\n                  className=\"mt-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.2)] p-6\"\n                >\n                  <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\n                    <div>\n                      <h2 className=\"text-xl font-bold text-[#0077B6] flex items-center\">\n                        <FaRobot className=\"mr-3 text-2xl\" />\n                        AI Analysis Results\n                      </h2>\n                      <p className=\"text-gray-600 text-sm mt-1\">Powered by YOLOv8 dental AI model</p>\n                    </div>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={generatePDFReport}\n                      className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-full font-semibold flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300\"\n                      title=\"Download comprehensive analysis report as PDF\"\n                    >\n                      <FaDownload className=\"text-lg\" />\n                      Download PDF Report\n                    </motion.button>\n                  </div>\n                  <div className=\"mb-6 p-4 bg-[rgba(0,119,182,0.05)] rounded-xl border border-[rgba(0,119,182,0.1)]\">\n                    <div className=\"flex items-center mb-3\">\n                      <div className=\"p-2 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                        <FaRobot className=\"text-lg\" />\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-semibold text-[#0077B6]\">YOLOv8 Dental AI Model</h3>\n                        <p className=\"text-xs text-gray-600\">Advanced deep learning for dental diagnostics</p>\n                      </div>\n                    </div>\n                    <div className=\"text-xs text-gray-700 leading-relaxed\">\n                      <strong>Detection Capabilities:</strong> Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth,\n                      Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures,\n                      Orthodontic brackets, and 18+ other dental conditions with high accuracy.\n                    </div>\n                  </div>\n                  {aiResult.findings.length > 0 ? (\n                    <>\n                      <div className=\"mb-4\">\n                        <h3 className=\"font-semibold text-[#0077B6] mb-3 flex items-center\">\n                          <FaSearch className=\"mr-2\" />\n                          Detected Conditions ({aiResult.findings.length})\n                        </h3>\n                        <div className=\"grid gap-3\">\n                          {aiResult.findings.map((f, idx) => {\n                            const confidenceLevel = f.confidence >= 0.8 ? 'high' : f.confidence >= 0.6 ? 'medium' : 'low';\n                            const confidenceColor = confidenceLevel === 'high' ? 'text-green-700 bg-green-100' :\n                                                   confidenceLevel === 'medium' ? 'text-yellow-700 bg-yellow-100' :\n                                                   'text-orange-700 bg-orange-100';\n                            const borderColor = confidenceLevel === 'high' ? 'border-green-200' :\n                                              confidenceLevel === 'medium' ? 'border-yellow-200' :\n                                              'border-orange-200';\n\n                            return (\n                              <motion.div\n                                key={idx}\n                                initial={{ opacity: 0, x: -20 }}\n                                animate={{ opacity: 1, x: 0 }}\n                                transition={{ delay: idx * 0.1 }}\n                                className={`bg-white p-4 rounded-xl border-2 ${borderColor} shadow-sm hover:shadow-md transition-all duration-300`}\n                              >\n                                <div className=\"flex items-center justify-between\">\n                                  <div className=\"flex items-center space-x-3\">\n                                    <div className=\"w-8 h-8 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6] font-bold text-sm\">\n                                      {idx + 1}\n                                    </div>\n                                    <span className=\"font-semibold text-gray-800\">{f.label}</span>\n                                  </div>\n                                  <div className=\"flex items-center space-x-2\">\n                                    <span className={`text-xs px-3 py-1 rounded-full font-medium ${confidenceColor}`}>\n                                      {confidenceLevel.toUpperCase()}\n                                    </span>\n                                    <span className=\"text-sm font-bold text-[#0077B6] bg-[rgba(0,119,182,0.1)] px-3 py-1 rounded-full\">\n                                      {(f.confidence * 100).toFixed(1)}%\n                                    </span>\n                                  </div>\n                                </div>\n                              </motion.div>\n                            );\n                          })}\n                        </div>\n                      </div>\n\n                      <div className=\"p-4 bg-[rgba(0,119,182,0.05)] rounded-xl border border-[rgba(0,119,182,0.1)]\">\n                        <div className=\"flex items-start space-x-3\">\n                          <FaCheckCircle className=\"text-[#0077B6] text-xl mt-0.5\" />\n                          <div>\n                            <h4 className=\"font-semibold text-[#0077B6] mb-1\">Analysis Summary</h4>\n                            <p className=\"text-gray-700 text-sm\">{aiResult.summary}</p>\n                          </div>\n                        </div>\n                      </div>\n                    </>\n                  ) : (\n                    <div className=\"p-6 bg-green-50 rounded-xl border-2 border-green-200 text-center\">\n                      <div className=\"flex flex-col items-center space-y-3\">\n                        <div className=\"w-16 h-16 rounded-full bg-green-100 flex items-center justify-center\">\n                          <FaCheckCircle className=\"text-green-600 text-2xl\" />\n                        </div>\n                        <div>\n                          <h3 className=\"font-semibold text-green-800 mb-1\">Excellent News!</h3>\n                          <p className=\"text-green-700 text-sm\">No significant dental pathology detected in this X-ray image.</p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                  {annotatedImageUrl && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4 }}\n                      className=\"mt-6\"\n                    >\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h3 className=\"font-semibold text-[#0077B6] flex items-center\">\n                          <FaEye className=\"mr-2\" />\n                          Annotated X-Ray Image\n                        </h3>\n                        <span className=\"text-xs bg-[rgba(0,119,182,0.1)] text-[#0077B6] px-2 py-1 rounded-full\">\n                          AI Annotations\n                        </span>\n                      </div>\n\n                      <div className=\"relative\">\n                        <img\n                          src={annotatedImageUrl}\n                          alt=\"Annotated X-Ray\"\n                          className=\"w-full max-w-2xl mx-auto rounded-xl border-2 border-[rgba(0,119,182,0.3)] shadow-lg hover:shadow-xl transition-all duration-300\"\n                          onError={(e) => {\n                            console.error('Failed to load annotated image:', annotatedImageUrl);\n                            e.target.style.display = 'none';\n                            e.target.nextSibling.style.display = 'block';\n                          }}\n                          onLoad={() => {\n                            console.log('Annotated image loaded successfully:', annotatedImageUrl);\n                          }}\n                        />\n                        <div className=\"absolute top-2 right-2 bg-[#0077B6] text-white px-2 py-1 rounded-lg text-xs font-medium\">\n                          <FaRobot className=\"inline mr-1\" />\n                          AI Detected\n                        </div>\n\n                        <div\n                          className=\"mt-4 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n                          style={{ display: 'none' }}\n                        >\n                          <div className=\"flex items-center\">\n                            <FaExclamationTriangle className=\"h-5 w-5 text-red-500 mr-3\" />\n                            <div>\n                              <p className=\"text-red-700 font-medium\">Could not load annotated image</p>\n                              <p className=\"text-red-600 text-sm\">The analysis was successful, but the annotated image is not available.</p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"mt-4 p-3 bg-[rgba(0,119,182,0.05)] rounded-lg border border-[rgba(0,119,182,0.1)]\">\n                        <p className=\"text-xs text-gray-600 text-center\">\n                          <FaEye className=\"inline mr-1\" />\n                          Bounding boxes and labels show detected dental conditions with confidence scores\n                        </p>\n                      </div>\n                    </motion.div>\n                  )}\n                </motion.div>\n              )}\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default XRay;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,WAAW,QAAQ,gBAAgB;AACpJ,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BnB,eAAe,CAACiB,IAAI,CAAC;IACrBb,WAAW,CAAC,IAAI,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;IACZE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAIG,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMpB,aAAa,CAACkB,MAAM,CAACG,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLf,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC1B,YAAY,EAAE;IACnBO,UAAU,CAAC,IAAI,CAAC;IAChBF,WAAW,CAAC,IAAI,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;IACZE,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAMc,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7B,YAAY,CAAC;MACtC,MAAM8B,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,QAAQ,EAAE;QAC/FQ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACC,OAAO,EAAE;QAC1ChC,WAAW,CAAC;UACViC,QAAQ,EAAER,QAAQ,CAACM,IAAI,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,KAAK;YAAEC,KAAK,EAAED,CAAC,CAACE,KAAK;YAAEC,UAAU,EAAEH,CAAC,CAACG;UAAW,CAAC,CAAC,CAAC;UACxFC,OAAO,EAAE,gBAAgBd,QAAQ,CAACM,IAAI,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,KAAK,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;QAC7E,CAAC,CAAC;QACF,IAAIf,QAAQ,CAACM,IAAI,CAACU,kBAAkB,EAAE;UACpC;UACA,MAAMC,QAAQ,GAAGjB,QAAQ,CAACM,IAAI,CAACU,kBAAkB,CAACE,KAAK,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC;UACtE,MAAMC,YAAY,GAAG,GAAGlB,OAAO,CAACC,GAAG,CAACC,iBAAiB,6BAA6Ba,QAAQ,EAAE;UAC5FI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,YAAY,CAAC;UACzDrC,oBAAoB,CAACqC,YAAY,CAAC;QACpC;MACF,CAAC,MAAM;QACLvC,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZ5C,QAAQ,CAAC,EAAA2C,aAAA,GAAAD,GAAG,CAACvB,QAAQ,cAAAwB,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAclB,IAAI,cAAAmB,kBAAA,uBAAlBA,kBAAA,CAAoB7C,KAAK,KAAI,gCAAgC,CAAC;IACzE,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACxD,YAAY,EAAE;IACnBS,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZI,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMY,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7B,YAAY,CAAC;MACtC,MAAM8B,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,QAAQ,EAAE;QAC/FQ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACqB,aAAa,EAAE;QAChD1C,mBAAmB,CAACe,QAAQ,CAACM,IAAI,CAACqB,aAAa,CAAC;MAClD,CAAC,MAAM;QACL9C,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAAK,cAAA,EAAAC,mBAAA;MACZhD,QAAQ,CAAC,EAAA+C,cAAA,GAAAL,GAAG,CAACvB,QAAQ,cAAA4B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActB,IAAI,cAAAuB,mBAAA,uBAAlBA,mBAAA,CAAoBjD,KAAK,KAAI,gCAAgC,CAAC;IACzE,CAAC,SAAS;MACRD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxD,QAAQ,IAAI,CAACA,QAAQ,CAACkC,QAAQ,EAAE;MACnCuB,KAAK,CAAC,uDAAuD,CAAC;MAC9D;IACF;IAEA,IAAI;MACF,MAAMC,GAAG,GAAG,IAAIvE,KAAK,CAAC,CAAC;MACvB,MAAMwE,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,KAAK;MAC7C,MAAMC,UAAU,GAAGL,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACG,MAAM;MAC/C,IAAIC,SAAS,GAAG,EAAE;;MAElB;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;MAC7BT,GAAG,CAACU,IAAI,CAAC,iCAAiC,EAAET,SAAS,GAAG,CAAC,EAAEM,SAAS,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;MAC1FJ,SAAS,IAAI,EAAE;;MAEf;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B,MAAMG,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MAC/Cd,GAAG,CAACU,IAAI,CAAC,iBAAiBE,WAAW,EAAE,EAAEX,SAAS,GAAG,CAAC,EAAEM,SAAS,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFJ,SAAS,IAAI,EAAE;;MAEf;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBT,GAAG,CAACU,IAAI,CAAC,sBAAsB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC/CA,SAAS,IAAI,EAAE;MAEfP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACU,IAAI,CAAC,iDAAiD,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC1EA,SAAS,IAAI,CAAC;MACdP,GAAG,CAACU,IAAI,CAAC,wDAAwD,EAAE,EAAE,EAAEH,SAAS,CAAC;MACjFA,SAAS,IAAI,CAAC;MACdP,GAAG,CAACU,IAAI,CAAC,4EAA4E,EAAE,EAAE,EAAEH,SAAS,CAAC;MACrGA,SAAS,IAAI,EAAE;;MAEf;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBT,GAAG,CAACU,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC3CA,SAAS,IAAI,EAAE;MAEf,IAAIjE,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,GAAG,CAAC,EAAE;QAChC;QACA,MAAMC,SAAS,GAAG1E,QAAQ,CAACkC,QAAQ,CAACC,GAAG,CAAC,CAACwC,OAAO,EAAEC,KAAK,KAAK,CAC1D,CAACA,KAAK,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,EACtBF,OAAO,CAACtC,KAAK,EACb,GAAG,CAACsC,OAAO,CAACpC,UAAU,GAAG,GAAG,EAAEuC,OAAO,CAAC,CAAC,CAAC,GAAG,EAC3CH,OAAO,CAACpC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGoC,OAAO,CAACpC,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAK,CAClF,CAAC;;QAEF;QACAmB,GAAG,CAACqB,SAAS,CAAC;UACZC,MAAM,EAAEf,SAAS;UACjBgB,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;UACvDC,IAAI,EAAER,SAAS;UACfS,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE;YAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAAEC,SAAS,EAAE;UAAI,CAAC;UACxDC,kBAAkB,EAAE;YAAEF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAAE,CAAC;UAClDG,MAAM,EAAE;YAAEC,IAAI,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAG,CAAC;UAC/BC,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAG;QACzB,CAAC,CAAC;QAEF3B,SAAS,GAAGP,GAAG,CAACmC,aAAa,CAACC,MAAM,GAAG,EAAE;;QAEzC;QACApC,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;QAC7BT,GAAG,CAACU,IAAI,CAAC,UAAU,EAAE,EAAE,EAAEH,SAAS,CAAC;QACnCA,SAAS,IAAI,CAAC;QAEdP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM4B,WAAW,GAAG/F,QAAQ,CAACwC,OAAO,IAAI,eAAexC,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,oCAAoC;QACnH,MAAMuB,YAAY,GAAGtC,GAAG,CAACuC,eAAe,CAACF,WAAW,EAAEpC,SAAS,GAAG,EAAE,CAAC;QACrED,GAAG,CAACU,IAAI,CAAC4B,YAAY,EAAE,EAAE,EAAE/B,SAAS,CAAC;QACrCA,SAAS,IAAI+B,YAAY,CAACvB,MAAM,GAAG,CAAC,GAAG,EAAE;MAE3C,CAAC,MAAM;QACLf,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC3BT,GAAG,CAACU,IAAI,CAAC,iEAAiE,EAAE,EAAE,EAAEH,SAAS,CAAC;QAC1FA,SAAS,IAAI,EAAE;MACjB;;MAEA;MACA,IAAInE,UAAU,EAAE;QACd;QACA,IAAImE,SAAS,GAAG,EAAE,GAAGF,UAAU,GAAG,EAAE,EAAE;UACpCL,GAAG,CAACwC,OAAO,CAAC,CAAC;UACbjC,SAAS,GAAG,EAAE;QAChB;QAEAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzBT,GAAG,CAACU,IAAI,CAAC,cAAc,EAAE,EAAE,EAAEH,SAAS,CAAC;QACvCA,SAAS,IAAI,EAAE;QAEf,IAAI;UACF;UACA,MAAMkC,QAAQ,GAAG,EAAE;UACnB,MAAMC,SAAS,GAAG,EAAE;UAEpB1C,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;UACnBR,GAAG,CAACU,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAEH,SAAS,CAAC;;UAE1C;UACAP,GAAG,CAAC2C,QAAQ,CAACvG,UAAU,EAAE,MAAM,EAAE,EAAE,EAAEmE,SAAS,GAAG,CAAC,EAAEkC,QAAQ,EAAEC,SAAS,CAAC;;UAExE;UACA,IAAI1F,gBAAgB,EAAE;YACpBgD,GAAG,CAACU,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAEH,SAAS,CAAC;YAC3CP,GAAG,CAAC2C,QAAQ,CAAC3F,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAEuD,SAAS,GAAG,CAAC,EAAEkC,QAAQ,EAAEC,SAAS,CAAC;UAChF;UAEAnC,SAAS,IAAImC,SAAS,GAAG,EAAE;QAC7B,CAAC,CAAC,OAAOE,QAAQ,EAAE;UACjBvD,OAAO,CAACwD,IAAI,CAAC,8BAA8B,EAAED,QAAQ,CAAC;UACtD5C,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;UACnBR,GAAG,CAACS,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAC/BT,GAAG,CAACU,IAAI,CAAC,+CAA+C,EAAE,EAAE,EAAEH,SAAS,CAAC;UACxEA,SAAS,IAAI,EAAE;QACjB;MACF;;MAEA;MACA,IAAIA,SAAS,GAAG,EAAE,GAAGF,UAAU,GAAG,EAAE,EAAE;QACpCL,GAAG,CAACwC,OAAO,CAAC,CAAC;QACbjC,SAAS,GAAGF,UAAU,GAAG,EAAE;MAC7B,CAAC,MAAM;QACLE,SAAS,GAAGF,UAAU,GAAG,EAAE;MAC7B;MAEAL,GAAG,CAACQ,WAAW,CAAC,CAAC,CAAC;MAClBR,GAAG,CAACS,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BT,GAAG,CAACU,IAAI,CAAC,mDAAmD,EAAET,SAAS,GAAG,CAAC,EAAEM,SAAS,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;MAC5GX,GAAG,CAACU,IAAI,CAAC,6EAA6E,EAAET,SAAS,GAAG,CAAC,EAAEM,SAAS,GAAG,CAAC,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE1I;MACA,MAAMmC,QAAQ,GAAG,2BAA2B,IAAIjC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAAC7D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACxFc,GAAG,CAACgD,IAAI,CAACF,QAAQ,CAAC;IAEpB,CAAC,CAAC,OAAOlG,KAAK,EAAE;MACdyC,OAAO,CAACzC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CmD,KAAK,CAAC,kDAAkD,CAAC;IAC3D;EACF,CAAC;EAED,oBACEpE,OAAA;IAAKsH,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCvH,OAAA,CAAChB,cAAc;MAACwI,MAAM,EAAEnH,WAAY;MAACoH,SAAS,EAAEnH;IAAe;MAAA6G,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClE5H,OAAA;MAAKsH,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDvH,OAAA,CAACf,MAAM;QAAC4I,aAAa,EAAEA,CAAA,KAAMvH,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA8G,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D5H,OAAA;QAAMsH,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGvH,OAAA;UAAKsH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCvH,OAAA,CAACJ,MAAM,CAACkI,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAZ,QAAA,gBAE9BvH,OAAA;cAAKsH,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FvH,OAAA;gBAAAuH,QAAA,gBACEvH,OAAA;kBAAIsH,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5H,OAAA;kBAAGsH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAuE;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEvH,OAAA,CAACT,OAAO;kBAAC+H,SAAS,EAAC;gBAAgB;kBAAAH,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtC5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAe;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5H,OAAA,CAACJ,MAAM,CAACkI,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAG,CAAE;cAC/BH,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAE,CAAE;cAC9BF,UAAU,EAAE;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC3Bf,SAAS,EAAC,mHAAmH;cAAAC,QAAA,eAE7HvH,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA,CAACJ,MAAM,CAACoD,KAAK;kBACXsE,SAAS,EAAC,iDAAiD;kBAC3DgB,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAhB,QAAA,gBAE1BvH,OAAA;oBAAKsH,SAAS,EAAC,4HAA4H;oBAAAC,QAAA,eACzIvH,OAAA,CAACd,QAAQ;sBAACoI,SAAS,EAAC;oBAAU;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACN5H,OAAA;oBAAMsH,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1F5H,OAAA;oBAAMsH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAiD;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChG5H,OAAA;oBAAOyI,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACpB,SAAS,EAAC,QAAQ;oBAACqB,QAAQ,EAAEpH;kBAAiB;oBAAA4F,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,EAEdnH,UAAU,iBACTT,OAAA,CAACJ,MAAM,CAACkI,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEO,KAAK,EAAE;kBAAI,CAAE;kBACpCN,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEO,KAAK,EAAE;kBAAE,CAAE;kBAClCL,UAAU,EAAE;oBAAEG,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,MAAM;kBAAAC,QAAA,eAEhBvH,OAAA;oBAAKsH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCvH,OAAA;sBACE4I,GAAG,EAAEnI,UAAW;sBAChBoI,GAAG,EAAC,eAAe;sBACnBvB,SAAS,EAAC;oBAA4E;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvF,CAAC,eACF5H,OAAA;sBAAKsH,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,gBACtGvH,OAAA,CAACP,OAAO;wBAAC6H,SAAS,EAAC;sBAAa;wBAAAH,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAErC;oBAAA;sBAAAT,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACb,EAEArH,YAAY,iBACXP,OAAA,CAACJ,MAAM,CAACkI,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE;kBAAG,CAAE;kBAC/BH,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEG,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,gBAE/DvH,OAAA,CAACJ,MAAM,CAACkJ,MAAM;oBACZR,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAC1BjB,SAAS,EAAC,4NAA4N;oBACtOyB,OAAO,EAAEhF,aAAc;oBACvBiF,QAAQ,EAAE,CAACzI,YAAY,IAAIQ,SAAU;oBAAAwG,QAAA,gBAErCvH,OAAA,CAACV,OAAO;sBAACgI,SAAS,EAAC;oBAAS;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC9B7G,SAAS,GAAG,cAAc,GAAG,eAAe;kBAAA;oBAAAoG,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eAChB5H,OAAA,CAACJ,MAAM,CAACkJ,MAAM;oBACZR,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAC1BjB,SAAS,EAAC,4NAA4N;oBACtOyB,OAAO,EAAE9G,aAAc;oBACvB+G,QAAQ,EAAE,CAACzI,YAAY,IAAIM,OAAQ;oBAAA0G,QAAA,gBAEnCvH,OAAA,CAACT,OAAO;sBAAC+H,SAAS,EAAC;oBAAS;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC9B/G,OAAO,GAAG,cAAc,GAAG,aAAa;kBAAA;oBAAAsG,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACb;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZ3G,KAAK,iBACJjB,OAAA,CAACJ,MAAM,CAACkI,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCH,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAE,CAAE;cAC9Bd,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAE7EvH,OAAA;gBAAKsH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvH,OAAA,CAACX,qBAAqB;kBAACiI,SAAS,EAAC;gBAA2B;kBAAAH,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/D5H,OAAA;kBAAGsH,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAEtG;gBAAK;kBAAAkG,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EACAvG,gBAAgB,iBACfrB,OAAA,CAACJ,MAAM,CAACkI,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAG,CAAE;cAC/BH,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAE,CAAE;cAC9BF,UAAU,EAAE;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC3Bf,SAAS,EAAC,yHAAyH;cAAAC,QAAA,gBAEnIvH,OAAA;gBAAKsH,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDvH,OAAA;kBAAIsH,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBAChEvH,OAAA,CAACV,OAAO;oBAACgI,SAAS,EAAC;kBAAe;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,0BAEvC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5H,OAAA;kBAAKsH,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAChEvH,OAAA;oBAAKsH,SAAS,EAAC;kBAAiD;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE5H,OAAA;oBAAAuH,QAAA,EAAM;kBAAoB;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5H,OAAA;gBAAKsH,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDvH,OAAA;kBAAKsH,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBvH,OAAA;oBAAKsH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDvH,OAAA;sBAAIsH,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,gBAC3DvH,OAAA,CAACL,WAAW;wBAAC2H,SAAS,EAAC;sBAAqB;wBAAAH,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,kBAEjD;oBAAA;sBAAAT,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5H,OAAA;sBAAMsH,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAAM;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACN5H,OAAA;oBAAKsH,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvH,OAAA;sBACE4I,GAAG,EAAEnI,UAAW;sBAChBoI,GAAG,EAAC,gBAAgB;sBACpBvB,SAAS,EAAC;oBAAkG;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7G;kBAAC;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN5H,OAAA;kBAAKsH,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBvH,OAAA;oBAAKsH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDvH,OAAA;sBAAIsH,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,gBAC5DvH,OAAA,CAACV,OAAO;wBAACgI,SAAS,EAAC;sBAAM;wBAAAH,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,kBAE9B;oBAAA;sBAAAT,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5H,OAAA;sBAAMsH,SAAS,EAAC,yEAAyE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnG,CAAC,eACN5H,OAAA;oBAAKsH,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBvH,OAAA;sBACE4I,GAAG,EAAEvH,gBAAiB;sBACtBwH,GAAG,EAAC,gBAAgB;sBACpBvB,SAAS,EAAC;oBAAgH;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3H,CAAC,eACF5H,OAAA;sBAAKsH,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,gBACtGvH,OAAA,CAACV,OAAO;wBAACgI,SAAS,EAAC;sBAAa;wBAAAH,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAErC;oBAAA;sBAAAT,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5H,OAAA;gBAAKsH,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,eAClGvH,OAAA;kBAAKsH,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCvH,OAAA,CAACZ,aAAa;oBAACkI,SAAS,EAAC;kBAA+B;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3D5H,OAAA;oBAAAuH,QAAA,gBACEvH,OAAA;sBAAIsH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAgC;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvF5H,OAAA;sBAAGsH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAIrC;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EACAjH,QAAQ,iBACPX,OAAA,CAACJ,MAAM,CAACkI,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAG,CAAE;cAC/BH,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAE,CAAE;cAC9BF,UAAU,EAAE;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAC3Bf,SAAS,EAAC,wHAAwH;cAAAC,QAAA,gBAElIvH,OAAA;gBAAKsH,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAC/FvH,OAAA;kBAAAuH,QAAA,gBACEvH,OAAA;oBAAIsH,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,gBAChEvH,OAAA,CAACT,OAAO;sBAAC+H,SAAS,EAAC;oBAAe;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,uBAEvC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5H,OAAA;oBAAGsH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAiC;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACN5H,OAAA,CAACJ,MAAM,CAACkJ,MAAM;kBACZR,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BQ,OAAO,EAAE5E,iBAAkB;kBAC3BmD,SAAS,EAAC,4MAA4M;kBACtN2B,KAAK,EAAC,+CAA+C;kBAAA1B,QAAA,gBAErDvH,OAAA,CAACR,UAAU;oBAAC8H,SAAS,EAAC;kBAAS;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAEpC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,gBAChGvH,OAAA;kBAAKsH,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCvH,OAAA;oBAAKsH,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,eAC5EvH,OAAA,CAACT,OAAO;sBAAC+H,SAAS,EAAC;oBAAS;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACN5H,OAAA;oBAAAuH,QAAA,gBACEvH,OAAA;sBAAIsH,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChF5H,OAAA;sBAAGsH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6C;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAKsH,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDvH,OAAA;oBAAAuH,QAAA,EAAQ;kBAAuB;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gOAG1C;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLjH,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,GAAG,CAAC,gBAC3BpF,OAAA,CAAAE,SAAA;gBAAAqH,QAAA,gBACEvH,OAAA;kBAAKsH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBvH,OAAA;oBAAIsH,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,gBACjEvH,OAAA,CAACb,QAAQ;sBAACmI,SAAS,EAAC;oBAAM;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,yBACR,EAACjH,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,EAAC,GACjD;kBAAA;oBAAA+B,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5H,OAAA;oBAAKsH,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACxB5G,QAAQ,CAACkC,QAAQ,CAACC,GAAG,CAAC,CAACoG,CAAC,EAAEC,GAAG,KAAK;sBACjC,MAAMC,eAAe,GAAGF,CAAC,CAAChG,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGgG,CAAC,CAAChG,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAK;sBAC7F,MAAMmG,eAAe,GAAGD,eAAe,KAAK,MAAM,GAAG,6BAA6B,GAC3DA,eAAe,KAAK,QAAQ,GAAG,+BAA+B,GAC9D,+BAA+B;sBACtD,MAAME,WAAW,GAAGF,eAAe,KAAK,MAAM,GAAG,kBAAkB,GACjDA,eAAe,KAAK,QAAQ,GAAG,mBAAmB,GAClD,mBAAmB;sBAErC,oBACEpJ,OAAA,CAACJ,MAAM,CAACkI,GAAG;wBAETC,OAAO,EAAE;0BAAEC,OAAO,EAAE,CAAC;0BAAEuB,CAAC,EAAE,CAAC;wBAAG,CAAE;wBAChCtB,OAAO,EAAE;0BAAED,OAAO,EAAE,CAAC;0BAAEuB,CAAC,EAAE;wBAAE,CAAE;wBAC9BrB,UAAU,EAAE;0BAAEG,KAAK,EAAEc,GAAG,GAAG;wBAAI,CAAE;wBACjC7B,SAAS,EAAE,oCAAoCgC,WAAW,wDAAyD;wBAAA/B,QAAA,eAEnHvH,OAAA;0BAAKsH,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDvH,OAAA;4BAAKsH,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,gBAC1CvH,OAAA;8BAAKsH,SAAS,EAAC,iHAAiH;8BAAAC,QAAA,EAC7H4B,GAAG,GAAG;4BAAC;8BAAAhC,QAAA,EAAAO,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACN5H,OAAA;8BAAMsH,SAAS,EAAC,6BAA6B;8BAAAC,QAAA,EAAE2B,CAAC,CAAClG;4BAAK;8BAAAmE,QAAA,EAAAO,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAT,QAAA,EAAAO,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3D,CAAC,eACN5H,OAAA;4BAAKsH,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,gBAC1CvH,OAAA;8BAAMsH,SAAS,EAAE,8CAA8C+B,eAAe,EAAG;8BAAA9B,QAAA,EAC9E6B,eAAe,CAACI,WAAW,CAAC;4BAAC;8BAAArC,QAAA,EAAAO,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACP5H,OAAA;8BAAMsH,SAAS,EAAC,kFAAkF;8BAAAC,QAAA,GAC/F,CAAC2B,CAAC,CAAChG,UAAU,GAAG,GAAG,EAAEuC,OAAO,CAAC,CAAC,CAAC,EAAC,GACnC;4BAAA;8BAAA0B,QAAA,EAAAO,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAT,QAAA,EAAAO,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA;0BAAAT,QAAA,EAAAO,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC,GArBDuB,GAAG;wBAAAhC,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsBE,CAAC;oBAEjB,CAAC;kBAAC;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN5H,OAAA;kBAAKsH,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,eAC3FvH,OAAA;oBAAKsH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzCvH,OAAA,CAACZ,aAAa;sBAACkI,SAAS,EAAC;oBAA+B;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3D5H,OAAA;sBAAAuH,QAAA,gBACEvH,OAAA;wBAAIsH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAJ,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvE5H,OAAA;wBAAGsH,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAE5G,QAAQ,CAACwC;sBAAO;wBAAAgE,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAT,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,eACN,CAAC,gBAEH5H,OAAA;gBAAKsH,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,eAC/EvH,OAAA;kBAAKsH,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,gBACnDvH,OAAA;oBAAKsH,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnFvH,OAAA,CAACZ,aAAa;sBAACkI,SAAS,EAAC;oBAAyB;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACN5H,OAAA;oBAAAuH,QAAA,gBACEvH,OAAA;sBAAIsH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtE5H,OAAA;sBAAGsH,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAC;oBAA6D;sBAAAJ,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpG,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EACAzG,iBAAiB,iBAChBnB,OAAA,CAACJ,MAAM,CAACkI,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEI,CAAC,EAAE;gBAAG,CAAE;gBAC/BH,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEI,CAAC,EAAE;gBAAE,CAAE;gBAC9BF,UAAU,EAAE;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBAC3Bf,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAEhBvH,OAAA;kBAAKsH,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDvH,OAAA;oBAAIsH,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC5DvH,OAAA,CAACN,KAAK;sBAAC4H,SAAS,EAAC;oBAAM;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,yBAE5B;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5H,OAAA;oBAAMsH,SAAS,EAAC,wEAAwE;oBAAAC,QAAA,EAAC;kBAEzF;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN5H,OAAA;kBAAKsH,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBvH,OAAA;oBACE4I,GAAG,EAAEzH,iBAAkB;oBACvB0H,GAAG,EAAC,iBAAiB;oBACrBvB,SAAS,EAAC,iIAAiI;oBAC3ImC,OAAO,EAAGjI,CAAC,IAAK;sBACdkC,OAAO,CAACzC,KAAK,CAAC,iCAAiC,EAAEE,iBAAiB,CAAC;sBACnEK,CAAC,CAACE,MAAM,CAACgI,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC/BnI,CAAC,CAACE,MAAM,CAACkI,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,OAAO;oBAC9C,CAAE;oBACFE,MAAM,EAAEA,CAAA,KAAM;sBACZnG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAExC,iBAAiB,CAAC;oBACxE;kBAAE;oBAAAgG,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF5H,OAAA;oBAAKsH,SAAS,EAAC,yFAAyF;oBAAAC,QAAA,gBACtGvH,OAAA,CAACT,OAAO;sBAAC+H,SAAS,EAAC;oBAAa;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAEN5H,OAAA;oBACEsH,SAAS,EAAC,mEAAmE;oBAC7EoC,KAAK,EAAE;sBAAEC,OAAO,EAAE;oBAAO,CAAE;oBAAApC,QAAA,eAE3BvH,OAAA;sBAAKsH,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCvH,OAAA,CAACX,qBAAqB;wBAACiI,SAAS,EAAC;sBAA2B;wBAAAH,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/D5H,OAAA;wBAAAuH,QAAA,gBACEvH,OAAA;0BAAGsH,SAAS,EAAC,0BAA0B;0BAAAC,QAAA,EAAC;wBAA8B;0BAAAJ,QAAA,EAAAO,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC1E5H,OAAA;0BAAGsH,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,EAAC;wBAAsE;0BAAAJ,QAAA,EAAAO,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAT,QAAA,EAAAO,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3G,CAAC;oBAAA;sBAAAT,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN5H,OAAA;kBAAKsH,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,eAChGvH,OAAA;oBAAGsH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAC9CvH,OAAA,CAACN,KAAK;sBAAC4H,SAAS,EAAC;oBAAa;sBAAAH,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oFAEnC;kBAAA;oBAAAT,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CACb;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAT,QAAA,EAAAO,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxH,EAAA,CAnlBID,IAAI;AAAA2J,EAAA,GAAJ3J,IAAI;AAqlBV,eAAeA,IAAI;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}