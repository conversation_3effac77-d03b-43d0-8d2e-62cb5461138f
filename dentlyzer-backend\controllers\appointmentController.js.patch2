// This is a patch file for appointmentController.js
// Add this function to update an appointment with a patient ID

// Update an appointment with a patient ID
const updateAppointmentPatient = async (req, res) => {
  try {
    const { id } = req.params;
    const { patientId } = req.body;

    if (!patientId) {
      return res.status(400).json({ message: 'Patient ID is required' });
    }

    // Find the appointment
    const appointment = await Appointment.findById(id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Validate that the patient exists
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Update the appointment
    appointment.patient = patientId;
    const updatedAppointment = await appointment.save();

    // Add the appointment to the patient's appointments list if not already there
    if (!patient.appointments.includes(updatedAppointment._id)) {
      await Patient.findByIdAndUpdate(patientId, {
        $push: { appointments: updatedAppointment._id }
      });
      console.log(`Added appointment ${updatedAppointment._id} to patient ${patientId}'s appointments list`);
    }

    // Return the updated appointment
    const populatedAppointment = await Appointment.findById(updatedAppointment._id)
      .populate('patient', 'nationalId fullName');

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error updating appointment patient:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

// Add this to the module.exports
module.exports = {
  // ... existing exports
  updateAppointmentPatient,
};
