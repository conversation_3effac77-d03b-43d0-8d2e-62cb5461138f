{"name": "dently<PERSON>-frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^12.18.1", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "motion-dom": "^12.18.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "react-scripts": "^5.0.1", "react-webcam": "^7.2.0", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}