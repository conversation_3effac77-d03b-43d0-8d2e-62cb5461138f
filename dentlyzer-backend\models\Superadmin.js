const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const SuperadminSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  plainPassword: { type: String }, // Store the unhashed password
  name: { type: String, required: true },
  role: { type: String, default: 'superadmin' },
}, { timestamps: true });

// Hash password before saving
SuperadminSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  // Store the plain password
  this.plainPassword = this.password;

  // Hash the password
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

module.exports = mongoose.model('Superadmin', SuperadminSchema);