const mongoose = require('mongoose');

// Sub-schema for time slots
const TimeSlotSchema = new mongoose.Schema({
  date: { type: Date, required: true },
  time: { type: String, required: true }, // e.g., "14:30"
  isAvailable: { type: Boolean, default: true },
  duration: { type: Number, default: 120 }, // in minutes, default to 120
}, { _id: false });

// Sub-schema for bilingual strings
const BilingualStringSchema = new mongoose.Schema({
  en: { type: String, required: true },
  ar: { type: String, required: true },
}, { _id: false });

// Sub-schema for bilingual dentistry services
const DentistryServiceSchema = new mongoose.Schema({
  en: { type: String, required: true },
  ar: { type: String, required: true },
}, { _id: false });

// Main University schema
const UniversitySchema = new mongoose.Schema({
  universityId: { type: String, required: true, unique: true }, // Unique ID like 'AIU'
  name: BilingualStringSchema,
  description: BilingualStringSchema,
  dentistryInfo: BilingualStringSchema,
  facilities: BilingualStringSchema,
  program: BilingualStringSchema,
  dentistryServices: [DentistryServiceSchema],
  address: {
    street: BilingualStringSchema,
    city: BilingualStringSchema,
    country: BilingualStringSchema,
    postalCode: { type: String },
  },
  contactInfo: {
    phone: { type: String, required: true },
    email: { type: String, required: true },
    website: { type: String },
  },
  image: { type: String },
  logo: { type: String },
  mapUrl: { type: String },
  timeSlots: [TimeSlotSchema],
  slotBeginDate: { type: Date, required: true }, // Slots beginning date
  slotEndDate: { type: Date, required: true }, // Slots ending date
  holidays: [{ type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] }],
  students: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Student' }],
  supervisors: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Supervisor' }],
  admins: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Admin' }],
  assistants: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Assistant' }],
}, { timestamps: true });

module.exports = mongoose.model('University', UniversitySchema);