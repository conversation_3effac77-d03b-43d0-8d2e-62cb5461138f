#!/usr/bin/env python3
"""
Test script for X-ray enhancement functionality
"""
import sys
import os
import cv2
import numpy as np

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_enhance import enhance_dental_xray

def test_enhancement():
    """Test the enhancement function with a sample image"""
    # Create a sample test image (simulating a low-quality X-ray)
    test_image = np.random.randint(0, 256, (400, 400), dtype=np.uint8)
    test_input_path = 'test_input.jpg'
    test_output_path = 'test_output_enhanced.png'
    
    try:
        # Save test image
        cv2.imwrite(test_input_path, test_image)
        print(f"✅ Created test image: {test_input_path}")
        
        # Test enhancement
        enhanced = enhance_dental_xray(test_input_path, test_output_path)
        print(f"✅ Enhancement completed: {test_output_path}")
        
        # Verify output exists
        if os.path.exists(test_output_path):
            print(f"✅ Enhanced image saved successfully")
            # Clean up
            os.remove(test_input_path)
            os.remove(test_output_path)
            print("✅ Test completed successfully!")
            return True
        else:
            print("❌ Enhanced image not found")
            return False
            
    except Exception as e:
        print(f"❌ Enhancement test failed: {str(e)}")
        # Clean up on error
        if os.path.exists(test_input_path):
            os.remove(test_input_path)
        if os.path.exists(test_output_path):
            os.remove(test_output_path)
        return False

if __name__ == "__main__":
    print("🧪 Testing X-ray enhancement functionality...")
    success = test_enhancement()
    sys.exit(0 if success else 1)
