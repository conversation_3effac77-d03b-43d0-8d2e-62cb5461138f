import React, { useState } from 'react';
import DentistSidebar from './DentistSidebar';
import Navbar from './Navbar';
import { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot, FaDownload, FaImage, FaEye, FaFileImage } from 'react-icons/fa';
import { motion } from 'framer-motion';
import axios from 'axios';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const XRay = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [aiResult, setAiResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [enhancing, setEnhancing] = useState(false);
  const [error, setError] = useState('');
  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);
  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setSelectedFile(file);
    setAiResult(null);
    setError('');
    setAnnotatedImageUrl(null);
    setEnhancedImageUrl(null);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setPreviewUrl(reader.result);
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }
  };

  const handleAnalyze = async () => {
    if (!selectedFile) return;
    setLoading(true);
    setAiResult(null);
    setError('');
    setAnnotatedImageUrl(null);
    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response.data && response.data.results) {
        setAiResult({
          findings: response.data.results.map(r => ({ label: r.class, confidence: r.confidence })),
          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`
        });
        if (response.data.annotatedImagePath) {
          // Extract filename from the full path (handle both forward and back slashes)
          const filename = response.data.annotatedImagePath.split(/[/\\]/).pop();
          const annotatedUrl = `${process.env.REACT_APP_API_URL}/api/xray/annotated-image/${filename}`;
          console.log('Setting annotated image URL:', annotatedUrl);
          setAnnotatedImageUrl(annotatedUrl);
        }
      } else {
        setError('No results from AI analysis.');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to analyze X-ray image.');
    } finally {
      setLoading(false);
    }
  };

  const handleEnhance = async () => {
    if (!selectedFile) return;
    setEnhancing(true);
    setError('');
    setEnhancedImageUrl(null);
    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response.data && response.data.enhancedImage) {
        setEnhancedImageUrl(response.data.enhancedImage);
      } else {
        setError('No enhanced image received from server.');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to enhance X-ray image.');
    } finally {
      setEnhancing(false);
    }
  };

  const generatePDFReport = async () => {
    if (!aiResult || !aiResult.findings) {
      alert('No analysis results available to generate PDF report.');
      return;
    }

    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.width;
      const pageHeight = pdf.internal.pageSize.height;
      let yPosition = 20;

      // Header
      pdf.setFontSize(20);
      pdf.setTextColor(0, 100, 200);
      pdf.text('Dentlyzer X-Ray Analysis Report', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 15;

      // Date and time
      pdf.setFontSize(12);
      pdf.setTextColor(100, 100, 100);
      const currentDate = new Date().toLocaleString();
      pdf.text(`Generated on: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 20;

      // AI Model Information
      pdf.setFontSize(14);
      pdf.setTextColor(0, 0, 0);
      pdf.text('AI Model Information', 20, yPosition);
      yPosition += 10;

      pdf.setFontSize(10);
      pdf.text('• Model: YOLOv8 trained on dental X-ray dataset', 25, yPosition);
      yPosition += 6;
      pdf.text('• Capabilities: Detects 31 different dental conditions', 25, yPosition);
      yPosition += 6;
      pdf.text('• Conditions include: Caries, Crown, Filling, Implant, Missing teeth, etc.', 25, yPosition);
      yPosition += 15;

      // Analysis Results
      pdf.setFontSize(14);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Analysis Results', 20, yPosition);
      yPosition += 10;

      if (aiResult.findings.length > 0) {
        // Create table data for detected conditions
        const tableData = aiResult.findings.map((finding, index) => [
          (index + 1).toString(),
          finding.label,
          `${(finding.confidence * 100).toFixed(1)}%`,
          finding.confidence >= 0.7 ? 'High' : finding.confidence >= 0.5 ? 'Medium' : 'Low'
        ]);

        // Add table
        pdf.autoTable({
          startY: yPosition,
          head: [['#', 'Condition', 'Confidence', 'Reliability']],
          body: tableData,
          theme: 'grid',
          headStyles: { fillColor: [0, 100, 200], textColor: 255 },
          alternateRowStyles: { fillColor: [245, 245, 245] },
          margin: { left: 20, right: 20 },
          styles: { fontSize: 10 }
        });

        yPosition = pdf.lastAutoTable.finalY + 15;

        // Summary
        pdf.setFontSize(12);
        pdf.setTextColor(0, 100, 200);
        pdf.text('Summary:', 20, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setTextColor(0, 0, 0);
        const summaryText = aiResult.summary || `AI detected ${aiResult.findings.length} condition(s) in this X-ray image.`;
        const splitSummary = pdf.splitTextToSize(summaryText, pageWidth - 40);
        pdf.text(splitSummary, 20, yPosition);
        yPosition += splitSummary.length * 6 + 10;

      } else {
        pdf.setFontSize(12);
        pdf.setTextColor(0, 150, 0);
        pdf.text('✓ No significant dental pathology detected in this X-ray image.', 20, yPosition);
        yPosition += 15;
      }

      // Add images if available
      if (previewUrl) {
        // Check if we have space for images
        if (yPosition + 80 > pageHeight - 20) {
          pdf.addPage();
          yPosition = 20;
        }

        pdf.setFontSize(14);
        pdf.setTextColor(0, 0, 0);
        pdf.text('X-Ray Images', 20, yPosition);
        yPosition += 15;

        try {
          // Add original image
          const imgWidth = 70;
          const imgHeight = 50;

          pdf.setFontSize(10);
          pdf.text('Original X-Ray:', 20, yPosition);

          // Convert base64 image to add to PDF
          pdf.addImage(previewUrl, 'JPEG', 20, yPosition + 5, imgWidth, imgHeight);

          // Add enhanced image if available
          if (enhancedImageUrl) {
            pdf.text('Enhanced X-Ray:', 110, yPosition);
            pdf.addImage(enhancedImageUrl, 'PNG', 110, yPosition + 5, imgWidth, imgHeight);
          }

          yPosition += imgHeight + 15;
        } catch (imgError) {
          console.warn('Could not add images to PDF:', imgError);
          pdf.setFontSize(10);
          pdf.setTextColor(150, 150, 150);
          pdf.text('(Images could not be included in this report)', 20, yPosition);
          yPosition += 10;
        }
      }

      // Footer
      if (yPosition + 30 > pageHeight - 20) {
        pdf.addPage();
        yPosition = pageHeight - 40;
      } else {
        yPosition = pageHeight - 40;
      }

      pdf.setFontSize(8);
      pdf.setTextColor(100, 100, 100);
      pdf.text('This report was generated by Dentlyzer AI system.', pageWidth / 2, yPosition, { align: 'center' });
      pdf.text('For medical decisions, please consult with a qualified dental professional.', pageWidth / 2, yPosition + 8, { align: 'center' });

      // Save the PDF
      const fileName = `Dentlyzer_XRay_Analysis_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF report. Please try again.');
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <DentistSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    X-Ray Analysis & Enhancement
                  </h1>
                  <p className="text-[#333333]">AI-powered dental X-ray analysis with advanced enhancement capabilities</p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <FaRobot className="text-[#0077B6]" />
                  <span>YOLOv8 AI Model</span>
                </div>
              </div>
              {/* Upload Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-8"
              >
                <div className="text-center">
                  <motion.label
                    className="flex flex-col items-center cursor-pointer group"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="p-6 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6] group-hover:bg-[rgba(0,119,182,0.15)] transition-all duration-300">
                      <FaUpload className="text-4xl" />
                    </div>
                    <span className="text-[#0077B6] font-semibold text-lg mt-4 mb-2">Upload X-Ray Image</span>
                    <span className="text-gray-500 text-sm">Click to select or drag and drop your X-ray image</span>
                    <input type="file" accept="image/*" className="hidden" onChange={handleFileChange} />
                  </motion.label>

                  {previewUrl && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.2 }}
                      className="mt-6"
                    >
                      <div className="relative inline-block">
                        <img
                          src={previewUrl}
                          alt="X-Ray Preview"
                          className="w-full max-w-sm rounded-xl border-2 border-[rgba(0,119,182,0.2)] shadow-lg"
                        />
                        <div className="absolute top-2 right-2 bg-[#0077B6] text-white px-2 py-1 rounded-lg text-xs font-medium">
                          <FaImage className="inline mr-1" />
                          Original
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {selectedFile && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="flex flex-col sm:flex-row gap-4 mt-8 justify-center"
                    >
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-gradient-to-r from-[#20B2AA] to-[#48CAE4] text-white px-8 py-3 rounded-full font-semibold flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300"
                        onClick={handleEnhance}
                        disabled={!selectedFile || enhancing}
                      >
                        <FaMagic className="text-lg" />
                        {enhancing ? 'Enhancing...' : 'Enhance X-Ray'}
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-3 rounded-full font-semibold flex items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300"
                        onClick={handleAnalyze}
                        disabled={!selectedFile || loading}
                      >
                        <FaRobot className="text-lg" />
                        {loading ? 'Analyzing...' : 'AI Analysis'}
                      </motion.button>
                    </motion.div>
                  )}
                </div>
              </motion.div>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
                >
                  <div className="flex items-center">
                    <FaExclamationTriangle className="h-5 w-5 text-red-500 mr-3" />
                    <p className="text-red-700 font-medium">{error}</p>
                  </div>
                </motion.div>
              )}
              {enhancedImageUrl && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="mt-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(32,178,170,0.2)] p-6"
                >
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-bold text-[#20B2AA] flex items-center">
                      <FaMagic className="mr-3 text-2xl" />
                      Enhanced X-Ray Results
                    </h2>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Enhancement Complete</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-gray-700 flex items-center">
                          <FaFileImage className="mr-2 text-[#0077B6]" />
                          Original Image
                        </h3>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">Before</span>
                      </div>
                      <div className="relative">
                        <img
                          src={previewUrl}
                          alt="Original X-Ray"
                          className="w-full rounded-xl border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-[#20B2AA] flex items-center">
                          <FaMagic className="mr-2" />
                          Enhanced Image
                        </h3>
                        <span className="text-xs bg-[rgba(32,178,170,0.1)] text-[#20B2AA] px-2 py-1 rounded-full">After</span>
                      </div>
                      <div className="relative">
                        <img
                          src={enhancedImageUrl}
                          alt="Enhanced X-Ray"
                          className="w-full rounded-xl border-2 border-[rgba(32,178,170,0.3)] shadow-lg hover:shadow-xl transition-all duration-300"
                        />
                        <div className="absolute top-2 right-2 bg-[#20B2AA] text-white px-2 py-1 rounded-lg text-xs font-medium">
                          <FaMagic className="inline mr-1" />
                          Enhanced
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-[rgba(32,178,170,0.05)] rounded-lg border border-[rgba(32,178,170,0.2)]">
                    <div className="flex items-start space-x-3">
                      <FaCheckCircle className="text-[#20B2AA] text-xl mt-0.5" />
                      <div>
                        <h4 className="font-semibold text-[#20B2AA] mb-1">Enhancement Applied Successfully</h4>
                        <p className="text-gray-700 text-sm">
                          Your X-ray image has been enhanced using advanced AI algorithms including noise reduction,
                          contrast enhancement, edge sharpening, and brightness optimization for better visibility
                          of dental structures.
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
              {aiResult && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mt-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.2)] p-6"
                >
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                    <div>
                      <h2 className="text-xl font-bold text-[#0077B6] flex items-center">
                        <FaRobot className="mr-3 text-2xl" />
                        AI Analysis Results
                      </h2>
                      <p className="text-gray-600 text-sm mt-1">Powered by YOLOv8 dental AI model</p>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={generatePDFReport}
                      className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-full font-semibold flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300"
                      title="Download comprehensive analysis report as PDF"
                    >
                      <FaDownload className="text-lg" />
                      Download PDF Report
                    </motion.button>
                  </div>
                  <div className="mb-6 p-4 bg-[rgba(0,119,182,0.05)] rounded-xl border border-[rgba(0,119,182,0.1)]">
                    <div className="flex items-center mb-3">
                      <div className="p-2 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                        <FaRobot className="text-lg" />
                      </div>
                      <div>
                        <h3 className="text-sm font-semibold text-[#0077B6]">YOLOv8 Dental AI Model</h3>
                        <p className="text-xs text-gray-600">Advanced deep learning for dental diagnostics</p>
                      </div>
                    </div>
                    <div className="text-xs text-gray-700 leading-relaxed">
                      <strong>Detection Capabilities:</strong> Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth,
                      Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures,
                      Orthodontic brackets, and 18+ other dental conditions with high accuracy.
                    </div>
                  </div>
                  {aiResult.findings.length > 0 ? (
                    <>
                      <div className="mb-4">
                        <h3 className="font-semibold text-[#0077B6] mb-3 flex items-center">
                          <FaSearch className="mr-2" />
                          Detected Conditions ({aiResult.findings.length})
                        </h3>
                        <div className="grid gap-3">
                          {aiResult.findings.map((f, idx) => {
                            const confidenceLevel = f.confidence >= 0.8 ? 'high' : f.confidence >= 0.6 ? 'medium' : 'low';
                            const confidenceColor = confidenceLevel === 'high' ? 'text-green-700 bg-green-100' :
                                                   confidenceLevel === 'medium' ? 'text-yellow-700 bg-yellow-100' :
                                                   'text-orange-700 bg-orange-100';
                            const borderColor = confidenceLevel === 'high' ? 'border-green-200' :
                                              confidenceLevel === 'medium' ? 'border-yellow-200' :
                                              'border-orange-200';

                            return (
                              <motion.div
                                key={idx}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: idx * 0.1 }}
                                className={`bg-white p-4 rounded-xl border-2 ${borderColor} shadow-sm hover:shadow-md transition-all duration-300`}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6] font-bold text-sm">
                                      {idx + 1}
                                    </div>
                                    <span className="font-semibold text-gray-800">{f.label}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className={`text-xs px-3 py-1 rounded-full font-medium ${confidenceColor}`}>
                                      {confidenceLevel.toUpperCase()}
                                    </span>
                                    <span className="text-sm font-bold text-[#0077B6] bg-[rgba(0,119,182,0.1)] px-3 py-1 rounded-full">
                                      {(f.confidence * 100).toFixed(1)}%
                                    </span>
                                  </div>
                                </div>
                              </motion.div>
                            );
                          })}
                        </div>
                      </div>

                      <div className="p-4 bg-[rgba(0,119,182,0.05)] rounded-xl border border-[rgba(0,119,182,0.1)]">
                        <div className="flex items-start space-x-3">
                          <FaCheckCircle className="text-[#0077B6] text-xl mt-0.5" />
                          <div>
                            <h4 className="font-semibold text-[#0077B6] mb-1">Analysis Summary</h4>
                            <p className="text-gray-700 text-sm">{aiResult.summary}</p>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="p-6 bg-green-50 rounded-xl border-2 border-green-200 text-center">
                      <div className="flex flex-col items-center space-y-3">
                        <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                          <FaCheckCircle className="text-green-600 text-2xl" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-green-800 mb-1">Excellent News!</h3>
                          <p className="text-green-700 text-sm">No significant dental pathology detected in this X-ray image.</p>
                        </div>
                      </div>
                    </div>
                  )}
                  {annotatedImageUrl && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="mt-6"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-[#0077B6] flex items-center">
                          <FaEye className="mr-2" />
                          Annotated X-Ray Image
                        </h3>
                        <span className="text-xs bg-[rgba(0,119,182,0.1)] text-[#0077B6] px-2 py-1 rounded-full">
                          AI Annotations
                        </span>
                      </div>

                      <div className="relative">
                        <img
                          src={annotatedImageUrl}
                          alt="Annotated X-Ray"
                          className="w-full max-w-2xl mx-auto rounded-xl border-2 border-[rgba(0,119,182,0.3)] shadow-lg hover:shadow-xl transition-all duration-300"
                          onError={(e) => {
                            console.error('Failed to load annotated image:', annotatedImageUrl);
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'block';
                          }}
                          onLoad={() => {
                            console.log('Annotated image loaded successfully:', annotatedImageUrl);
                          }}
                        />
                        <div className="absolute top-2 right-2 bg-[#0077B6] text-white px-2 py-1 rounded-lg text-xs font-medium">
                          <FaRobot className="inline mr-1" />
                          AI Detected
                        </div>

                        <div
                          className="mt-4 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
                          style={{ display: 'none' }}
                        >
                          <div className="flex items-center">
                            <FaExclamationTriangle className="h-5 w-5 text-red-500 mr-3" />
                            <div>
                              <p className="text-red-700 font-medium">Could not load annotated image</p>
                              <p className="text-red-600 text-sm">The analysis was successful, but the annotated image is not available.</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 p-3 bg-[rgba(0,119,182,0.05)] rounded-lg border border-[rgba(0,119,182,0.1)]">
                        <p className="text-xs text-gray-600 text-center">
                          <FaEye className="inline mr-1" />
                          Bounding boxes and labels show detected dental conditions with confidence scores
                        </p>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default XRay;