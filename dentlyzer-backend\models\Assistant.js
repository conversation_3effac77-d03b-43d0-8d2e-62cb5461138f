const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const AssistantSchema = new mongoose.Schema({
  assistantId: {
    type: String,
    required: true,
    unique: true,
    default: () => new mongoose.Types.ObjectId().toString() // System-generated ID
  },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  plainPassword: { type: String }, // Store the unhashed password
  name: { type: String, required: true },
  role: { type: String, default: 'assistant' },
  affiliation: {
    type: {
      type: String,
      enum: ['dentist', 'university'],
      required: true
    },
    id: {
      type: String,
      required: true
    } // References Dentist.dentistId or university string
  },
  phoneNumber: { type: String },
  // Only one of these will be set based on affiliation type
  university: { type: String },
  dentistId: { type: String }, // Not required, will be set based on affiliation type
}, { timestamps: true });

// Hash password before saving
AssistantSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  // Store the plain password
  this.plainPassword = this.password;

  // Hash the password
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

module.exports = mongoose.model('Assistant', AssistantSchema);