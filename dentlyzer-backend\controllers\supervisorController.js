const Supervisor = require('../models/Supervisor');
const Review = require('../models/Review');
const Patient = require('../models/Patient');
const Joi = require('joi');
const mongoose = require('mongoose');


// Validation schema for review submission with teeth chart
const reviewSchema = Joi.object({
  patientId: Joi.string().required(),
  studentId: Joi.string().required(),
  teethChart: Joi.object({
    title: Joi.string().required(),
    date: Joi.date().required(),
    teeth: Joi.array().items(
      Joi.object({
        toothNumber: Joi.number().required(),
        procedure: Joi.string().required()
      })
    ).required()
  }).optional(),
});

const submitReview = async (req, res) => {
  const { error } = reviewSchema.validate(req.body);
  if (error) return res.status(400).json({ message: error.details[0].message });

  const { patientId, studentId, teethChart } = req.body;

  try {
    const review = new Review({
      patientId,
      studentId,
      teethChart: teethChart || null, // Include teeth chart if provided
      status: 'pending',
      submittedDate: Date.now(),
    });
    await review.save();
    res.status(201).json(review);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getReviewById = async (req, res) => {
  try {
    const review = await Review.findById(req.params.id)
      .populate('patientId', 'fullName nationalId')
      .populate('studentId', 'name')
      .populate('supervisorId', 'name'); // Populate supervisor name if assigned
    if (!review) return res.status(404).json({ message: 'Review not found' });
    res.json(review);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Unchanged getStudentReviews function
const getStudentReviews = async (req, res) => {
  try {
    const reviews = await Review.find({ studentId: req.user.id })
      .populate('patientId', 'fullName nationalId')
      .populate('studentId', 'name')
      .populate('supervisorId', 'name');
    res.json(reviews);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getPendingReviews = async (req, res) => {
  try {
    const supervisor = await Supervisor.findById(req.user.id).populate({
      path: 'pendingReviews',
      populate: [
        { path: 'patientId', select: 'nationalId fullName age gender phoneNumber' }, // Minimal data
        { path: 'studentId', select: 'name' },
      ],
    });
    if (!supervisor) return res.status(404).json({ message: 'Supervisor not found' });

    res.json(supervisor.pendingReviews);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getCompletedReviews = async (req, res) => {
  try {
    const supervisor = await Supervisor.findById(req.user.id).populate({
      path: 'completedReviews',
      populate: [
        { path: 'patientId', select: 'nationalId fullName age gender phoneNumber' },
        { path: 'studentId', select: 'name' },
      ],
    });
    if (!supervisor) return res.status(404).json({ message: 'Supervisor not found' });

    res.json(supervisor.completedReviews);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const reviewCase = async (req, res) => {
  const { status, comment } = req.body;
  try {
    const review = await Review.findById(req.params.reviewId);
    if (!review) return res.status(404).json({ message: 'Review not found' });

    const supervisor = await Supervisor.findById(req.user.id);
    if (!supervisor.pendingReviews.includes(review._id)) {
      return res.status(403).json({ message: 'Not authorized to review this case' });
    }

    review.status = status;
    review.comment = comment || '';
    review.reviewedDate = new Date();
    await review.save();

    supervisor.pendingReviews = supervisor.pendingReviews.filter(
      (id) => id.toString() !== review._id.toString()
    );
    supervisor.completedReviews.push(review._id);
    await supervisor.save();

    res.json(review);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// New endpoint to get patient by nationalId
const getPatientByNationalId = async (req, res) => {
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId })
      .populate('teethCharts')
      .populate('appointments');
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    res.json(patient);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

/**
 * Get the supervisor's signature
 */
const getSignature = async (req, res) => {
  try {
    console.log('getSignature called for user:', req.user);

    // Find the supervisor by ID
    const supervisor = await Supervisor.findById(req.user.id);

    console.log('Supervisor found:', supervisor ? 'Yes' : 'No');

    if (!supervisor) {
      return res.status(404).json({ message: 'Supervisor not found' });
    }

    console.log('Supervisor details:', {
      id: supervisor._id,
      name: supervisor.name,
      email: supervisor.email,
      signature: supervisor.signature ? 'Exists' : 'Not set',
      signatureLength: supervisor.signature ? supervisor.signature.length : 0
    });

    console.log('Returning signature:', supervisor.signature ? 'Exists' : 'Not set');

    // Return the signature
    res.json({ signature: supervisor.signature });
  } catch (error) {
    console.error('Error fetching supervisor signature:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Save the supervisor's signature
 */
const saveSignature = async (req, res) => {
  try {
    console.log('saveSignature called for user:', req.user);

    const { signature } = req.body;
    console.log('Signature provided:', signature ? 'Yes' : 'No');

    if (!signature) {
      return res.status(400).json({ message: 'Signature is required' });
    }

    // Find the supervisor by ID
    const supervisor = await Supervisor.findById(req.user.id);
    console.log('Supervisor found:', supervisor ? 'Yes' : 'No');

    if (!supervisor) {
      return res.status(404).json({ message: 'Supervisor not found' });
    }

    // Update the signature
    supervisor.signature = signature;

    console.log('Before saving, supervisor signature:', {
      exists: supervisor.signature ? 'Yes' : 'No',
      length: supervisor.signature ? supervisor.signature.length : 0
    });

    await supervisor.save();

    // Verify the signature was saved
    const updatedSupervisor = await Supervisor.findById(req.user.id);
    console.log('After saving, supervisor signature:', {
      exists: updatedSupervisor.signature ? 'Yes' : 'No',
      length: updatedSupervisor.signature ? updatedSupervisor.signature.length : 0
    });

    console.log(`Signature saved for supervisor: ${supervisor.name} (${supervisor._id})`);
    res.json({ message: 'Signature saved successfully', signature });
  } catch (error) {
    console.error('Error saving supervisor signature:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getPendingReviews,
  getCompletedReviews,
  reviewCase,
  getPatientByNationalId,
  getSignature,
  saveSignature
};