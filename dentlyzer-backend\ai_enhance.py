import cv2
import numpy as np

def enhance_dental_xray(input_path, output_path):
    # Load dental X-ray image (grayscale)
    image = cv2.imread(input_path, cv2.IMREAD_GRAYSCALE)
    if image is None:
        raise ValueError("Failed to load image. Check the file path.")

    # Step 1: Denoising with Non-Local Means
    # Effective for removing noise in dental X-rays while preserving tooth boundaries
    denoised = cv2.fastNlMeansDenoising(image, h=10, templateWindowSize=7, searchWindowSize=21)

    # Step 2: Contrast Enhancement with CLAHE
    # Improves visibility of caries and periodontal tissue
    clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8, 8))  # Adjusted for dental X-rays
    contrast_enhanced = clahe.apply(denoised)

    # Step 3: Edge Enhancement with Adaptive Sharpening
    # Highlights tooth edges and caries lesions
    blurred = cv2.GaussianBlur(contrast_enhanced, (9, 9), sigmaX=2)
    sharpened = cv2.addWeighted(contrast_enhanced, 1.8, blurred, -0.8, 0)  # Stronger sharpening for dental details

    # Step 4: Morphological Operation to Reduce Artifacts
    # Removes small artifacts caused by prostheses or imaging errors
    kernel = np.ones((3, 3), np.uint8)
    cleaned = cv2.morphologyEx(sharpened, cv2.MORPH_OPEN, kernel, iterations=1)

    # Step 5: Gamma Correction for Brightness Adjustment
    # Enhances visibility of subtle structures like root canals
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    final_image = cv2.LUT(cleaned, table)

    # Save the enhanced image
    cv2.imwrite(output_path, final_image)
    return final_image

# Command line usage
if __name__ == "__main__":
    import sys

    if len(sys.argv) != 3:
        print("Usage: python ai_enhance.py <input_image_path> <output_image_path>")
        sys.exit(1)

    input_image_path = sys.argv[1]
    output_image_path = sys.argv[2]

    try:
        enhanced_image = enhance_dental_xray(input_image_path, output_image_path)
        print(f"Enhanced dental X-ray saved to {output_image_path}")
    except Exception as e:
        print(f"Error enhancing image: {str(e)}")
        sys.exit(1)