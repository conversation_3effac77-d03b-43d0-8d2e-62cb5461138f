{"ast": null, "code": "var _jsxFileName = \"D:\\\\New folder (3)\\\\dentlyzer-frontend\\\\src\\\\dentist\\\\XRay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport DentistSidebar from './DentistSidebar';\nimport Navbar from './Navbar';\nimport { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot, FaDownload, FaImage, FaEye, FaFileImage } from 'react-icons/fa';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XRay = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [aiResult, setAiResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [enhancing, setEnhancing] = useState(false);\n  const [error, setError] = useState('');\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    setEnhancedImageUrl(null);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setPreviewUrl(reader.result);\n      reader.readAsDataURL(file);\n    } else {\n      setPreviewUrl(null);\n    }\n  };\n  const handleAnalyze = async () => {\n    if (!selectedFile) return;\n    setLoading(true);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.results) {\n        setAiResult({\n          findings: response.data.results.map(r => ({\n            label: r.class,\n            confidence: r.confidence\n          })),\n          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`\n        });\n        if (response.data.annotatedImagePath) {\n          // Extract filename from the full path (handle both forward and back slashes)\n          const filename = response.data.annotatedImagePath.split(/[/\\\\]/).pop();\n          const annotatedUrl = `${process.env.REACT_APP_API_URL}/api/xray/annotated-image/${filename}`;\n          console.log('Setting annotated image URL:', annotatedUrl);\n          setAnnotatedImageUrl(annotatedUrl);\n        }\n      } else {\n        setError('No results from AI analysis.');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Failed to analyze X-ray image.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnhance = async () => {\n    if (!selectedFile) return;\n    setEnhancing(true);\n    setError('');\n    setEnhancedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.enhancedImage) {\n        setEnhancedImageUrl(response.data.enhancedImage);\n      } else {\n        setError('No enhanced image received from server.');\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || 'Failed to enhance X-ray image.');\n    } finally {\n      setEnhancing(false);\n    }\n  };\n  const generatePDFReport = async () => {\n    if (!aiResult || !aiResult.findings) {\n      alert('No analysis results available to generate PDF report.');\n      return;\n    }\n    try {\n      const pdf = new jsPDF();\n      const pageWidth = pdf.internal.pageSize.width;\n      const pageHeight = pdf.internal.pageSize.height;\n      let yPosition = 20;\n\n      // Header\n      pdf.setFontSize(20);\n      pdf.setTextColor(0, 100, 200);\n      pdf.text('Dentlyzer X-Ray Analysis Report', pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 15;\n\n      // Date and time\n      pdf.setFontSize(12);\n      pdf.setTextColor(100, 100, 100);\n      const currentDate = new Date().toLocaleString();\n      pdf.text(`Generated on: ${currentDate}`, pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 20;\n\n      // AI Model Information\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('AI Model Information', 20, yPosition);\n      yPosition += 10;\n      pdf.setFontSize(10);\n      pdf.text('• Model: YOLOv8 trained on dental X-ray dataset', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Capabilities: Detects 31 different dental conditions', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Conditions include: Caries, Crown, Filling, Implant, Missing teeth, etc.', 25, yPosition);\n      yPosition += 15;\n\n      // Analysis Results\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('Analysis Results', 20, yPosition);\n      yPosition += 10;\n      if (aiResult.findings.length > 0) {\n        // Create table data for detected conditions\n        const tableData = aiResult.findings.map((finding, index) => [(index + 1).toString(), finding.label, `${(finding.confidence * 100).toFixed(1)}%`, finding.confidence >= 0.7 ? 'High' : finding.confidence >= 0.5 ? 'Medium' : 'Low']);\n\n        // Add table\n        pdf.autoTable({\n          startY: yPosition,\n          head: [['#', 'Condition', 'Confidence', 'Reliability']],\n          body: tableData,\n          theme: 'grid',\n          headStyles: {\n            fillColor: [0, 100, 200],\n            textColor: 255\n          },\n          alternateRowStyles: {\n            fillColor: [245, 245, 245]\n          },\n          margin: {\n            left: 20,\n            right: 20\n          },\n          styles: {\n            fontSize: 10\n          }\n        });\n        yPosition = pdf.lastAutoTable.finalY + 15;\n\n        // Summary\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 100, 200);\n        pdf.text('Summary:', 20, yPosition);\n        yPosition += 8;\n        pdf.setFontSize(10);\n        pdf.setTextColor(0, 0, 0);\n        const summaryText = aiResult.summary || `AI detected ${aiResult.findings.length} condition(s) in this X-ray image.`;\n        const splitSummary = pdf.splitTextToSize(summaryText, pageWidth - 40);\n        pdf.text(splitSummary, 20, yPosition);\n        yPosition += splitSummary.length * 6 + 10;\n      } else {\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 150, 0);\n        pdf.text('✓ No significant dental pathology detected in this X-ray image.', 20, yPosition);\n        yPosition += 15;\n      }\n\n      // Add images if available\n      if (previewUrl) {\n        // Check if we have space for images\n        if (yPosition + 80 > pageHeight - 20) {\n          pdf.addPage();\n          yPosition = 20;\n        }\n        pdf.setFontSize(14);\n        pdf.setTextColor(0, 0, 0);\n        pdf.text('X-Ray Images', 20, yPosition);\n        yPosition += 15;\n        try {\n          // Add original image\n          const imgWidth = 70;\n          const imgHeight = 50;\n          pdf.setFontSize(10);\n          pdf.text('Original X-Ray:', 20, yPosition);\n\n          // Convert base64 image to add to PDF\n          pdf.addImage(previewUrl, 'JPEG', 20, yPosition + 5, imgWidth, imgHeight);\n\n          // Add enhanced image if available\n          if (enhancedImageUrl) {\n            pdf.text('Enhanced X-Ray:', 110, yPosition);\n            pdf.addImage(enhancedImageUrl, 'PNG', 110, yPosition + 5, imgWidth, imgHeight);\n          }\n          yPosition += imgHeight + 15;\n        } catch (imgError) {\n          console.warn('Could not add images to PDF:', imgError);\n          pdf.setFontSize(10);\n          pdf.setTextColor(150, 150, 150);\n          pdf.text('(Images could not be included in this report)', 20, yPosition);\n          yPosition += 10;\n        }\n      }\n\n      // Footer\n      if (yPosition + 30 > pageHeight - 20) {\n        pdf.addPage();\n        yPosition = pageHeight - 40;\n      } else {\n        yPosition = pageHeight - 40;\n      }\n      pdf.setFontSize(8);\n      pdf.setTextColor(100, 100, 100);\n      pdf.text('This report was generated by Dentlyzer AI system.', pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      pdf.text('For medical decisions, please consult with a qualified dental professional.', pageWidth / 2, yPosition + 8, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      const fileName = `Dentlyzer_XRay_Analysis_${new Date().toISOString().split('T')[0]}.pdf`;\n      pdf.save(fileName);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Failed to generate PDF report. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(DentistSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-blue-900 mb-8\",\n            children: \"X-Ray Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-md p-8 flex flex-col items-center gap-6 border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex flex-col items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                className: \"text-blue-600 text-3xl mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-700 font-medium mb-2\",\n                children: \"Upload X-Ray Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                onChange: handleFileChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n              src: previewUrl,\n              alt: \"X-Ray Preview\",\n              className: \"w-full max-w-xs rounded-lg border border-gray-200 shadow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-4 mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\",\n                onClick: handleEnhance,\n                disabled: !selectedFile || enhancing,\n                children: [/*#__PURE__*/_jsxDEV(FaMagic, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), \" \", enhancing ? 'Enhancing...' : 'Enhance X-Ray']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\",\n                onClick: handleAnalyze,\n                disabled: !selectedFile || loading,\n                children: [/*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), \" \", loading ? 'Analyzing...' : 'AI Analysis']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full mt-4 bg-red-50 rounded-lg p-4 border border-red-200 text-red-700 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), \" \", error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), enhancedImageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full mt-6 bg-green-50 rounded-lg p-4 border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-bold text-green-800 mb-2 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaMagic, {\n                  className: \"mr-2 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), \"Enhanced X-Ray Image\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-gray-700 mb-2\",\n                    children: \"Original:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: previewUrl,\n                    alt: \"Original X-Ray\",\n                    className: \"w-full max-w-sm rounded-lg border border-gray-300 shadow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-green-700 mb-2\",\n                    children: \"Enhanced:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: enhancedImageUrl,\n                    alt: \"Enhanced X-Ray\",\n                    className: \"w-full max-w-sm rounded-lg border border-green-300 shadow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 text-green-900 font-medium\",\n                children: \"\\u2705 X-ray image has been enhanced with improved contrast, reduced noise, and better visibility of dental structures.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), aiResult && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full mt-6 bg-blue-50 rounded-lg p-4 border border-blue-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg font-bold text-blue-800 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n                    className: \"mr-2 text-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 23\n                  }, this), \"AI Analysis Results\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: generatePDFReport,\n                  className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold flex items-center gap-2 text-sm\",\n                  title: \"Download analysis report as PDF\",\n                  children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 23\n                  }, this), \" Download PDF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4 p-3 bg-blue-100 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-700 font-medium mb-2\",\n                  children: \"\\uD83E\\uDD16 AI Model: YOLOv8 trained on dental X-ray dataset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-600\",\n                  children: \"Can detect: Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth, Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures, Orthodontic brackets, and 18+ other dental conditions.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), aiResult.findings.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-blue-800 mb-2\",\n                  children: \"Detected Conditions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"mb-3 space-y-1\",\n                  children: aiResult.findings.map((f, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"text-gray-700 flex items-center justify-between bg-white p-2 rounded border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-blue-700\",\n                      children: f.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm bg-blue-100 px-2 py-1 rounded\",\n                      children: [(f.confidence * 100).toFixed(1), \"% confidence\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 29\n                    }, this)]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-900 font-medium bg-blue-100 p-2 rounded\",\n                  children: [\"\\uD83D\\uDCCA \", aiResult.summary]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-green-700 bg-green-100 p-3 rounded-lg\",\n                children: \"\\u2705 No significant dental pathology detected in this X-ray image.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 21\n              }, this), annotatedImageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-blue-700 mb-2\",\n                  children: \"Annotated X-Ray Image:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: annotatedImageUrl,\n                  alt: \"Annotated X-Ray\",\n                  className: \"w-full max-w-md rounded-lg border border-blue-300 shadow\",\n                  onError: e => {\n                    console.error('Failed to load annotated image:', annotatedImageUrl);\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'block';\n                  },\n                  onLoad: () => {\n                    console.log('Annotated image loaded successfully:', annotatedImageUrl);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 bg-red-100 p-2 rounded mt-2\",\n                  style: {\n                    display: 'none'\n                  },\n                  children: \"\\u26A0\\uFE0F Could not load annotated image. The analysis was successful, but the annotated image is not available.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n_s(XRay, \"LggJ7dspVJn4gPaqFJYM5cPlyBU=\");\n_c = XRay;\nexport default XRay;\nvar _c;\n$RefreshReg$(_c, \"XRay\");", "map": {"version": 3, "names": ["React", "useState", "DentistSidebar", "<PERSON><PERSON><PERSON>", "FaUpload", "FaSearch", "FaCheckCircle", "FaExclamationTriangle", "FaMagic", "FaRobot", "FaDownload", "FaImage", "FaEye", "FaFileImage", "motion", "axios", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XRay", "_s", "sidebarOpen", "setSidebarOpen", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "aiResult", "setAiResult", "loading", "setLoading", "enhancing", "setEnhancing", "error", "setError", "annotatedImageUrl", "setAnnotatedImageUrl", "enhancedImageUrl", "setEnhancedImageUrl", "handleFileChange", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleAnalyze", "formData", "FormData", "append", "response", "post", "process", "env", "REACT_APP_API_URL", "headers", "data", "results", "findings", "map", "r", "label", "class", "confidence", "summary", "join", "annotatedImagePath", "filename", "split", "pop", "annotatedUrl", "console", "log", "err", "_err$response", "_err$response$data", "handleEnhance", "enhancedImage", "_err$response2", "_err$response2$data", "generatePDFReport", "alert", "pdf", "pageWidth", "internal", "pageSize", "width", "pageHeight", "height", "yPosition", "setFontSize", "setTextColor", "text", "align", "currentDate", "Date", "toLocaleString", "length", "tableData", "finding", "index", "toString", "toFixed", "autoTable", "startY", "head", "body", "theme", "headStyles", "fillColor", "textColor", "alternateRowStyles", "margin", "left", "right", "styles", "fontSize", "lastAutoTable", "finalY", "summaryText", "splitSummary", "splitTextToSize", "addPage", "imgWidth", "imgHeight", "addImage", "imgError", "warn", "fileName", "toISOString", "save", "className", "children", "isOpen", "setIsOpen", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "type", "accept", "onChange", "src", "alt", "onClick", "disabled", "title", "f", "idx", "onError", "style", "display", "nextS<PERSON>ling", "onLoad", "_c", "$RefreshReg$"], "sources": ["D:/New folder (3)/dentlyzer-frontend/src/dentist/XRay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport DentistSidebar from './DentistSidebar';\nimport Navbar from './Navbar';\nimport { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot, FaDownload, FaImage, FaEye, FaFileImage } from 'react-icons/fa';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\n\nconst XRay = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [aiResult, setAiResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [enhancing, setEnhancing] = useState(false);\n  const [error, setError] = useState('');\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    setEnhancedImageUrl(null);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setPreviewUrl(reader.result);\n      reader.readAsDataURL(file);\n    } else {\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleAnalyze = async () => {\n    if (!selectedFile) return;\n    setLoading(true);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' },\n      });\n      if (response.data && response.data.results) {\n        setAiResult({\n          findings: response.data.results.map(r => ({ label: r.class, confidence: r.confidence })),\n          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`\n        });\n        if (response.data.annotatedImagePath) {\n          // Extract filename from the full path (handle both forward and back slashes)\n          const filename = response.data.annotatedImagePath.split(/[/\\\\]/).pop();\n          const annotatedUrl = `${process.env.REACT_APP_API_URL}/api/xray/annotated-image/${filename}`;\n          console.log('Setting annotated image URL:', annotatedUrl);\n          setAnnotatedImageUrl(annotatedUrl);\n        }\n      } else {\n        setError('No results from AI analysis.');\n      }\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to analyze X-ray image.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEnhance = async () => {\n    if (!selectedFile) return;\n    setEnhancing(true);\n    setError('');\n    setEnhancedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' },\n      });\n      if (response.data && response.data.enhancedImage) {\n        setEnhancedImageUrl(response.data.enhancedImage);\n      } else {\n        setError('No enhanced image received from server.');\n      }\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to enhance X-ray image.');\n    } finally {\n      setEnhancing(false);\n    }\n  };\n\n  const generatePDFReport = async () => {\n    if (!aiResult || !aiResult.findings) {\n      alert('No analysis results available to generate PDF report.');\n      return;\n    }\n\n    try {\n      const pdf = new jsPDF();\n      const pageWidth = pdf.internal.pageSize.width;\n      const pageHeight = pdf.internal.pageSize.height;\n      let yPosition = 20;\n\n      // Header\n      pdf.setFontSize(20);\n      pdf.setTextColor(0, 100, 200);\n      pdf.text('Dentlyzer X-Ray Analysis Report', pageWidth / 2, yPosition, { align: 'center' });\n      yPosition += 15;\n\n      // Date and time\n      pdf.setFontSize(12);\n      pdf.setTextColor(100, 100, 100);\n      const currentDate = new Date().toLocaleString();\n      pdf.text(`Generated on: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' });\n      yPosition += 20;\n\n      // AI Model Information\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('AI Model Information', 20, yPosition);\n      yPosition += 10;\n\n      pdf.setFontSize(10);\n      pdf.text('• Model: YOLOv8 trained on dental X-ray dataset', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Capabilities: Detects 31 different dental conditions', 25, yPosition);\n      yPosition += 6;\n      pdf.text('• Conditions include: Caries, Crown, Filling, Implant, Missing teeth, etc.', 25, yPosition);\n      yPosition += 15;\n\n      // Analysis Results\n      pdf.setFontSize(14);\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('Analysis Results', 20, yPosition);\n      yPosition += 10;\n\n      if (aiResult.findings.length > 0) {\n        // Create table data for detected conditions\n        const tableData = aiResult.findings.map((finding, index) => [\n          (index + 1).toString(),\n          finding.label,\n          `${(finding.confidence * 100).toFixed(1)}%`,\n          finding.confidence >= 0.7 ? 'High' : finding.confidence >= 0.5 ? 'Medium' : 'Low'\n        ]);\n\n        // Add table\n        pdf.autoTable({\n          startY: yPosition,\n          head: [['#', 'Condition', 'Confidence', 'Reliability']],\n          body: tableData,\n          theme: 'grid',\n          headStyles: { fillColor: [0, 100, 200], textColor: 255 },\n          alternateRowStyles: { fillColor: [245, 245, 245] },\n          margin: { left: 20, right: 20 },\n          styles: { fontSize: 10 }\n        });\n\n        yPosition = pdf.lastAutoTable.finalY + 15;\n\n        // Summary\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 100, 200);\n        pdf.text('Summary:', 20, yPosition);\n        yPosition += 8;\n\n        pdf.setFontSize(10);\n        pdf.setTextColor(0, 0, 0);\n        const summaryText = aiResult.summary || `AI detected ${aiResult.findings.length} condition(s) in this X-ray image.`;\n        const splitSummary = pdf.splitTextToSize(summaryText, pageWidth - 40);\n        pdf.text(splitSummary, 20, yPosition);\n        yPosition += splitSummary.length * 6 + 10;\n\n      } else {\n        pdf.setFontSize(12);\n        pdf.setTextColor(0, 150, 0);\n        pdf.text('✓ No significant dental pathology detected in this X-ray image.', 20, yPosition);\n        yPosition += 15;\n      }\n\n      // Add images if available\n      if (previewUrl) {\n        // Check if we have space for images\n        if (yPosition + 80 > pageHeight - 20) {\n          pdf.addPage();\n          yPosition = 20;\n        }\n\n        pdf.setFontSize(14);\n        pdf.setTextColor(0, 0, 0);\n        pdf.text('X-Ray Images', 20, yPosition);\n        yPosition += 15;\n\n        try {\n          // Add original image\n          const imgWidth = 70;\n          const imgHeight = 50;\n\n          pdf.setFontSize(10);\n          pdf.text('Original X-Ray:', 20, yPosition);\n\n          // Convert base64 image to add to PDF\n          pdf.addImage(previewUrl, 'JPEG', 20, yPosition + 5, imgWidth, imgHeight);\n\n          // Add enhanced image if available\n          if (enhancedImageUrl) {\n            pdf.text('Enhanced X-Ray:', 110, yPosition);\n            pdf.addImage(enhancedImageUrl, 'PNG', 110, yPosition + 5, imgWidth, imgHeight);\n          }\n\n          yPosition += imgHeight + 15;\n        } catch (imgError) {\n          console.warn('Could not add images to PDF:', imgError);\n          pdf.setFontSize(10);\n          pdf.setTextColor(150, 150, 150);\n          pdf.text('(Images could not be included in this report)', 20, yPosition);\n          yPosition += 10;\n        }\n      }\n\n      // Footer\n      if (yPosition + 30 > pageHeight - 20) {\n        pdf.addPage();\n        yPosition = pageHeight - 40;\n      } else {\n        yPosition = pageHeight - 40;\n      }\n\n      pdf.setFontSize(8);\n      pdf.setTextColor(100, 100, 100);\n      pdf.text('This report was generated by Dentlyzer AI system.', pageWidth / 2, yPosition, { align: 'center' });\n      pdf.text('For medical decisions, please consult with a qualified dental professional.', pageWidth / 2, yPosition + 8, { align: 'center' });\n\n      // Save the PDF\n      const fileName = `Dentlyzer_XRay_Analysis_${new Date().toISOString().split('T')[0]}.pdf`;\n      pdf.save(fileName);\n\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Failed to generate PDF report. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <DentistSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <main className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h1 className=\"text-3xl font-bold text-blue-900 mb-8\">X-Ray Analysis</h1>\n            <div className=\"bg-white rounded-xl shadow-md p-8 flex flex-col items-center gap-6 border border-blue-100\">\n              <label className=\"flex flex-col items-center cursor-pointer\">\n                <FaUpload className=\"text-blue-600 text-3xl mb-2\" />\n                <span className=\"text-blue-700 font-medium mb-2\">Upload X-Ray Image</span>\n                <input type=\"file\" accept=\"image/*\" className=\"hidden\" onChange={handleFileChange} />\n              </label>\n              {previewUrl && (\n                <img src={previewUrl} alt=\"X-Ray Preview\" className=\"w-full max-w-xs rounded-lg border border-gray-200 shadow\" />\n              )}\n              <div className=\"flex gap-4 mt-4\">\n                <button\n                  className=\"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\"\n                  onClick={handleEnhance}\n                  disabled={!selectedFile || enhancing}\n                >\n                  <FaMagic /> {enhancing ? 'Enhancing...' : 'Enhance X-Ray'}\n                </button>\n                <button\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\"\n                  onClick={handleAnalyze}\n                  disabled={!selectedFile || loading}\n                >\n                  <FaRobot /> {loading ? 'Analyzing...' : 'AI Analysis'}\n                </button>\n              </div>\n              {error && (\n                <div className=\"w-full mt-4 bg-red-50 rounded-lg p-4 border border-red-200 text-red-700 flex items-center gap-2\">\n                  <FaExclamationTriangle /> {error}\n                </div>\n              )}\n              {enhancedImageUrl && (\n                <div className=\"w-full mt-6 bg-green-50 rounded-lg p-4 border border-green-200\">\n                  <h2 className=\"text-lg font-bold text-green-800 mb-2 flex items-center\">\n                    <FaMagic className=\"mr-2 text-green-500\" />Enhanced X-Ray Image\n                  </h2>\n                  <div className=\"flex flex-col md:flex-row gap-4\">\n                    <div className=\"flex-1\">\n                      <div className=\"font-semibold text-gray-700 mb-2\">Original:</div>\n                      <img src={previewUrl} alt=\"Original X-Ray\" className=\"w-full max-w-sm rounded-lg border border-gray-300 shadow\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"font-semibold text-green-700 mb-2\">Enhanced:</div>\n                      <img src={enhancedImageUrl} alt=\"Enhanced X-Ray\" className=\"w-full max-w-sm rounded-lg border border-green-300 shadow\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-3 text-green-900 font-medium\">\n                    ✅ X-ray image has been enhanced with improved contrast, reduced noise, and better visibility of dental structures.\n                  </div>\n                </div>\n              )}\n              {aiResult && (\n                <div className=\"w-full mt-6 bg-blue-50 rounded-lg p-4 border border-blue-200\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <h2 className=\"text-lg font-bold text-blue-800 flex items-center\">\n                      <FaRobot className=\"mr-2 text-blue-500\" />AI Analysis Results\n                    </h2>\n                    <button\n                      onClick={generatePDFReport}\n                      className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold flex items-center gap-2 text-sm\"\n                      title=\"Download analysis report as PDF\"\n                    >\n                      <FaDownload /> Download PDF\n                    </button>\n                  </div>\n                  <div className=\"mb-4 p-3 bg-blue-100 rounded-lg\">\n                    <div className=\"text-sm text-blue-700 font-medium mb-2\">\n                      🤖 AI Model: YOLOv8 trained on dental X-ray dataset\n                    </div>\n                    <div className=\"text-xs text-blue-600\">\n                      Can detect: Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth,\n                      Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures,\n                      Orthodontic brackets, and 18+ other dental conditions.\n                    </div>\n                  </div>\n                  {aiResult.findings.length > 0 ? (\n                    <>\n                      <h3 className=\"font-semibold text-blue-800 mb-2\">Detected Conditions:</h3>\n                      <ul className=\"mb-3 space-y-1\">\n                        {aiResult.findings.map((f, idx) => (\n                          <li key={idx} className=\"text-gray-700 flex items-center justify-between bg-white p-2 rounded border\">\n                            <span className=\"font-semibold text-blue-700\">{f.label}</span>\n                            <span className=\"text-sm bg-blue-100 px-2 py-1 rounded\">\n                              {(f.confidence * 100).toFixed(1)}% confidence\n                            </span>\n                          </li>\n                        ))}\n                      </ul>\n                      <div className=\"text-blue-900 font-medium bg-blue-100 p-2 rounded\">\n                        📊 {aiResult.summary}\n                      </div>\n                    </>\n                  ) : (\n                    <div className=\"text-green-700 bg-green-100 p-3 rounded-lg\">\n                      ✅ No significant dental pathology detected in this X-ray image.\n                    </div>\n                  )}\n                  {annotatedImageUrl && (\n                    <div className=\"mt-4\">\n                      <div className=\"font-semibold text-blue-700 mb-2\">Annotated X-Ray Image:</div>\n                      <img\n                        src={annotatedImageUrl}\n                        alt=\"Annotated X-Ray\"\n                        className=\"w-full max-w-md rounded-lg border border-blue-300 shadow\"\n                        onError={(e) => {\n                          console.error('Failed to load annotated image:', annotatedImageUrl);\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'block';\n                        }}\n                        onLoad={() => {\n                          console.log('Annotated image loaded successfully:', annotatedImageUrl);\n                        }}\n                      />\n                      <div\n                        className=\"text-red-600 bg-red-100 p-2 rounded mt-2\"\n                        style={{ display: 'none' }}\n                      >\n                        ⚠️ Could not load annotated image. The analysis was successful, but the annotated image is not available.\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default XRay;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,WAAW,QAAQ,gBAAgB;AACpJ,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BnB,eAAe,CAACiB,IAAI,CAAC;IACrBb,WAAW,CAAC,IAAI,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;IACZE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAIG,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMpB,aAAa,CAACkB,MAAM,CAACG,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLf,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC1B,YAAY,EAAE;IACnBO,UAAU,CAAC,IAAI,CAAC;IAChBF,WAAW,CAAC,IAAI,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;IACZE,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAMc,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7B,YAAY,CAAC;MACtC,MAAM8B,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,QAAQ,EAAE;QAC/FQ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACC,OAAO,EAAE;QAC1ChC,WAAW,CAAC;UACViC,QAAQ,EAAER,QAAQ,CAACM,IAAI,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,KAAK;YAAEC,KAAK,EAAED,CAAC,CAACE,KAAK;YAAEC,UAAU,EAAEH,CAAC,CAACG;UAAW,CAAC,CAAC,CAAC;UACxFC,OAAO,EAAE,gBAAgBd,QAAQ,CAACM,IAAI,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,KAAK,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;QAC7E,CAAC,CAAC;QACF,IAAIf,QAAQ,CAACM,IAAI,CAACU,kBAAkB,EAAE;UACpC;UACA,MAAMC,QAAQ,GAAGjB,QAAQ,CAACM,IAAI,CAACU,kBAAkB,CAACE,KAAK,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC;UACtE,MAAMC,YAAY,GAAG,GAAGlB,OAAO,CAACC,GAAG,CAACC,iBAAiB,6BAA6Ba,QAAQ,EAAE;UAC5FI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,YAAY,CAAC;UACzDrC,oBAAoB,CAACqC,YAAY,CAAC;QACpC;MACF,CAAC,MAAM;QACLvC,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZ5C,QAAQ,CAAC,EAAA2C,aAAA,GAAAD,GAAG,CAACvB,QAAQ,cAAAwB,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAclB,IAAI,cAAAmB,kBAAA,uBAAlBA,kBAAA,CAAoB7C,KAAK,KAAI,gCAAgC,CAAC;IACzE,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACxD,YAAY,EAAE;IACnBS,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZI,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMY,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7B,YAAY,CAAC;MACtC,MAAM8B,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,QAAQ,EAAE;QAC/FQ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACqB,aAAa,EAAE;QAChD1C,mBAAmB,CAACe,QAAQ,CAACM,IAAI,CAACqB,aAAa,CAAC;MAClD,CAAC,MAAM;QACL9C,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAAK,cAAA,EAAAC,mBAAA;MACZhD,QAAQ,CAAC,EAAA+C,cAAA,GAAAL,GAAG,CAACvB,QAAQ,cAAA4B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActB,IAAI,cAAAuB,mBAAA,uBAAlBA,mBAAA,CAAoBjD,KAAK,KAAI,gCAAgC,CAAC;IACzE,CAAC,SAAS;MACRD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxD,QAAQ,IAAI,CAACA,QAAQ,CAACkC,QAAQ,EAAE;MACnCuB,KAAK,CAAC,uDAAuD,CAAC;MAC9D;IACF;IAEA,IAAI;MACF,MAAMC,GAAG,GAAG,IAAIvE,KAAK,CAAC,CAAC;MACvB,MAAMwE,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,KAAK;MAC7C,MAAMC,UAAU,GAAGL,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACG,MAAM;MAC/C,IAAIC,SAAS,GAAG,EAAE;;MAElB;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;MAC7BT,GAAG,CAACU,IAAI,CAAC,iCAAiC,EAAET,SAAS,GAAG,CAAC,EAAEM,SAAS,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;MAC1FJ,SAAS,IAAI,EAAE;;MAEf;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B,MAAMG,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MAC/Cd,GAAG,CAACU,IAAI,CAAC,iBAAiBE,WAAW,EAAE,EAAEX,SAAS,GAAG,CAAC,EAAEM,SAAS,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFJ,SAAS,IAAI,EAAE;;MAEf;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBT,GAAG,CAACU,IAAI,CAAC,sBAAsB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC/CA,SAAS,IAAI,EAAE;MAEfP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACU,IAAI,CAAC,iDAAiD,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC1EA,SAAS,IAAI,CAAC;MACdP,GAAG,CAACU,IAAI,CAAC,wDAAwD,EAAE,EAAE,EAAEH,SAAS,CAAC;MACjFA,SAAS,IAAI,CAAC;MACdP,GAAG,CAACU,IAAI,CAAC,4EAA4E,EAAE,EAAE,EAAEH,SAAS,CAAC;MACrGA,SAAS,IAAI,EAAE;;MAEf;MACAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;MACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBT,GAAG,CAACU,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC3CA,SAAS,IAAI,EAAE;MAEf,IAAIjE,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,GAAG,CAAC,EAAE;QAChC;QACA,MAAMC,SAAS,GAAG1E,QAAQ,CAACkC,QAAQ,CAACC,GAAG,CAAC,CAACwC,OAAO,EAAEC,KAAK,KAAK,CAC1D,CAACA,KAAK,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,EACtBF,OAAO,CAACtC,KAAK,EACb,GAAG,CAACsC,OAAO,CAACpC,UAAU,GAAG,GAAG,EAAEuC,OAAO,CAAC,CAAC,CAAC,GAAG,EAC3CH,OAAO,CAACpC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGoC,OAAO,CAACpC,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAK,CAClF,CAAC;;QAEF;QACAmB,GAAG,CAACqB,SAAS,CAAC;UACZC,MAAM,EAAEf,SAAS;UACjBgB,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;UACvDC,IAAI,EAAER,SAAS;UACfS,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE;YAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAAEC,SAAS,EAAE;UAAI,CAAC;UACxDC,kBAAkB,EAAE;YAAEF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAAE,CAAC;UAClDG,MAAM,EAAE;YAAEC,IAAI,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAG,CAAC;UAC/BC,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAG;QACzB,CAAC,CAAC;QAEF3B,SAAS,GAAGP,GAAG,CAACmC,aAAa,CAACC,MAAM,GAAG,EAAE;;QAEzC;QACApC,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;QAC7BT,GAAG,CAACU,IAAI,CAAC,UAAU,EAAE,EAAE,EAAEH,SAAS,CAAC;QACnCA,SAAS,IAAI,CAAC;QAEdP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM4B,WAAW,GAAG/F,QAAQ,CAACwC,OAAO,IAAI,eAAexC,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,oCAAoC;QACnH,MAAMuB,YAAY,GAAGtC,GAAG,CAACuC,eAAe,CAACF,WAAW,EAAEpC,SAAS,GAAG,EAAE,CAAC;QACrED,GAAG,CAACU,IAAI,CAAC4B,YAAY,EAAE,EAAE,EAAE/B,SAAS,CAAC;QACrCA,SAAS,IAAI+B,YAAY,CAACvB,MAAM,GAAG,CAAC,GAAG,EAAE;MAE3C,CAAC,MAAM;QACLf,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC3BT,GAAG,CAACU,IAAI,CAAC,iEAAiE,EAAE,EAAE,EAAEH,SAAS,CAAC;QAC1FA,SAAS,IAAI,EAAE;MACjB;;MAEA;MACA,IAAInE,UAAU,EAAE;QACd;QACA,IAAImE,SAAS,GAAG,EAAE,GAAGF,UAAU,GAAG,EAAE,EAAE;UACpCL,GAAG,CAACwC,OAAO,CAAC,CAAC;UACbjC,SAAS,GAAG,EAAE;QAChB;QAEAP,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;QACnBR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzBT,GAAG,CAACU,IAAI,CAAC,cAAc,EAAE,EAAE,EAAEH,SAAS,CAAC;QACvCA,SAAS,IAAI,EAAE;QAEf,IAAI;UACF;UACA,MAAMkC,QAAQ,GAAG,EAAE;UACnB,MAAMC,SAAS,GAAG,EAAE;UAEpB1C,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;UACnBR,GAAG,CAACU,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAEH,SAAS,CAAC;;UAE1C;UACAP,GAAG,CAAC2C,QAAQ,CAACvG,UAAU,EAAE,MAAM,EAAE,EAAE,EAAEmE,SAAS,GAAG,CAAC,EAAEkC,QAAQ,EAAEC,SAAS,CAAC;;UAExE;UACA,IAAI1F,gBAAgB,EAAE;YACpBgD,GAAG,CAACU,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAEH,SAAS,CAAC;YAC3CP,GAAG,CAAC2C,QAAQ,CAAC3F,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAEuD,SAAS,GAAG,CAAC,EAAEkC,QAAQ,EAAEC,SAAS,CAAC;UAChF;UAEAnC,SAAS,IAAImC,SAAS,GAAG,EAAE;QAC7B,CAAC,CAAC,OAAOE,QAAQ,EAAE;UACjBvD,OAAO,CAACwD,IAAI,CAAC,8BAA8B,EAAED,QAAQ,CAAC;UACtD5C,GAAG,CAACQ,WAAW,CAAC,EAAE,CAAC;UACnBR,GAAG,CAACS,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAC/BT,GAAG,CAACU,IAAI,CAAC,+CAA+C,EAAE,EAAE,EAAEH,SAAS,CAAC;UACxEA,SAAS,IAAI,EAAE;QACjB;MACF;;MAEA;MACA,IAAIA,SAAS,GAAG,EAAE,GAAGF,UAAU,GAAG,EAAE,EAAE;QACpCL,GAAG,CAACwC,OAAO,CAAC,CAAC;QACbjC,SAAS,GAAGF,UAAU,GAAG,EAAE;MAC7B,CAAC,MAAM;QACLE,SAAS,GAAGF,UAAU,GAAG,EAAE;MAC7B;MAEAL,GAAG,CAACQ,WAAW,CAAC,CAAC,CAAC;MAClBR,GAAG,CAACS,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BT,GAAG,CAACU,IAAI,CAAC,mDAAmD,EAAET,SAAS,GAAG,CAAC,EAAEM,SAAS,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;MAC5GX,GAAG,CAACU,IAAI,CAAC,6EAA6E,EAAET,SAAS,GAAG,CAAC,EAAEM,SAAS,GAAG,CAAC,EAAE;QAAEI,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE1I;MACA,MAAMmC,QAAQ,GAAG,2BAA2B,IAAIjC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAAC7D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACxFc,GAAG,CAACgD,IAAI,CAACF,QAAQ,CAAC;IAEpB,CAAC,CAAC,OAAOlG,KAAK,EAAE;MACdyC,OAAO,CAACzC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CmD,KAAK,CAAC,kDAAkD,CAAC;IAC3D;EACF,CAAC;EAED,oBACEpE,OAAA;IAAKsH,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCvH,OAAA,CAAChB,cAAc;MAACwI,MAAM,EAAEnH,WAAY;MAACoH,SAAS,EAAEnH;IAAe;MAAA6G,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClE5H,OAAA;MAAKsH,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDvH,OAAA,CAACf,MAAM;QAAC4I,aAAa,EAAEA,CAAA,KAAMvH,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA8G,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D5H,OAAA;QAAMsH,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eAClFvH,OAAA;UAAKsH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvH,OAAA;YAAIsH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAc;YAAAJ,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE5H,OAAA;YAAKsH,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBACxGvH,OAAA;cAAOsH,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBAC1DvH,OAAA,CAACd,QAAQ;gBAACoI,SAAS,EAAC;cAA6B;gBAAAH,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpD5H,OAAA;gBAAMsH,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAkB;gBAAAJ,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1E5H,OAAA;gBAAO8H,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,SAAS;gBAACT,SAAS,EAAC,QAAQ;gBAACU,QAAQ,EAAEzG;cAAiB;gBAAA4F,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACPnH,UAAU,iBACTT,OAAA;cAAKiI,GAAG,EAAExH,UAAW;cAACyH,GAAG,EAAC,eAAe;cAACZ,SAAS,EAAC;YAA0D;cAAAH,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACjH,eACD5H,OAAA;cAAKsH,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BvH,OAAA;gBACEsH,SAAS,EAAC,2HAA2H;gBACrIa,OAAO,EAAEpE,aAAc;gBACvBqE,QAAQ,EAAE,CAAC7H,YAAY,IAAIQ,SAAU;gBAAAwG,QAAA,gBAErCvH,OAAA,CAACV,OAAO;kBAAA6H,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC7G,SAAS,GAAG,cAAc,GAAG,eAAe;cAAA;gBAAAoG,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACT5H,OAAA;gBACEsH,SAAS,EAAC,yHAAyH;gBACnIa,OAAO,EAAElG,aAAc;gBACvBmG,QAAQ,EAAE,CAAC7H,YAAY,IAAIM,OAAQ;gBAAA0G,QAAA,gBAEnCvH,OAAA,CAACT,OAAO;kBAAA4H,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC/G,OAAO,GAAG,cAAc,GAAG,aAAa;cAAA;gBAAAsG,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL3G,KAAK,iBACJjB,OAAA;cAAKsH,SAAS,EAAC,iGAAiG;cAAAC,QAAA,gBAC9GvH,OAAA,CAACX,qBAAqB;gBAAA8H,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAAC3G,KAAK;YAAA;cAAAkG,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CACN,EACAvG,gBAAgB,iBACfrB,OAAA;cAAKsH,SAAS,EAAC,gEAAgE;cAAAC,QAAA,gBAC7EvH,OAAA;gBAAIsH,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBACrEvH,OAAA,CAACV,OAAO;kBAACgI,SAAS,EAAC;gBAAqB;kBAAAH,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAC7C;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5H,OAAA;gBAAKsH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CvH,OAAA;kBAAKsH,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBvH,OAAA;oBAAKsH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjE5H,OAAA;oBAAKiI,GAAG,EAAExH,UAAW;oBAACyH,GAAG,EAAC,gBAAgB;oBAACZ,SAAS,EAAC;kBAA0D;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC,eACN5H,OAAA;kBAAKsH,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBvH,OAAA;oBAAKsH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAJ,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClE5H,OAAA;oBAAKiI,GAAG,EAAE5G,gBAAiB;oBAAC6G,GAAG,EAAC,gBAAgB;oBAACZ,SAAS,EAAC;kBAA2D;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAJ,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EACAjH,QAAQ,iBACPX,OAAA;cAAKsH,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3EvH,OAAA;gBAAKsH,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDvH,OAAA;kBAAIsH,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAC/DvH,OAAA,CAACT,OAAO;oBAAC+H,SAAS,EAAC;kBAAoB;oBAAAH,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAC5C;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5H,OAAA;kBACEmI,OAAO,EAAEhE,iBAAkB;kBAC3BmD,SAAS,EAAC,2GAA2G;kBACrHe,KAAK,EAAC,iCAAiC;kBAAAd,QAAA,gBAEvCvH,OAAA,CAACR,UAAU;oBAAA2H,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAChB;gBAAA;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CvH,OAAA;kBAAKsH,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAExD;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5H,OAAA;kBAAKsH,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAIvC;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLjH,QAAQ,CAACkC,QAAQ,CAACuC,MAAM,GAAG,CAAC,gBAC3BpF,OAAA,CAAAE,SAAA;gBAAAqH,QAAA,gBACEvH,OAAA;kBAAIsH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1E5H,OAAA;kBAAIsH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAC3B5G,QAAQ,CAACkC,QAAQ,CAACC,GAAG,CAAC,CAACwF,CAAC,EAAEC,GAAG,kBAC5BvI,OAAA;oBAAcsH,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,gBACnGvH,OAAA;sBAAMsH,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAEe,CAAC,CAACtF;oBAAK;sBAAAmE,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC9D5H,OAAA;sBAAMsH,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,GACpD,CAACe,CAAC,CAACpF,UAAU,GAAG,GAAG,EAAEuC,OAAO,CAAC,CAAC,CAAC,EAAC,cACnC;oBAAA;sBAAA0B,QAAA,EAAAO,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GAJAW,GAAG;oBAAApB,QAAA,EAAAO,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKR,CACL;gBAAC;kBAAAT,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACL5H,OAAA;kBAAKsH,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,eAC9D,EAAC5G,QAAQ,CAACwC,OAAO;gBAAA;kBAAAgE,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA,eACN,CAAC,gBAEH5H,OAAA;gBAAKsH,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAE5D;gBAAAJ,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAzG,iBAAiB,iBAChBnB,OAAA;gBAAKsH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvH,OAAA;kBAAKsH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9E5H,OAAA;kBACEiI,GAAG,EAAE9G,iBAAkB;kBACvB+G,GAAG,EAAC,iBAAiB;kBACrBZ,SAAS,EAAC,0DAA0D;kBACpEkB,OAAO,EAAGhH,CAAC,IAAK;oBACdkC,OAAO,CAACzC,KAAK,CAAC,iCAAiC,EAAEE,iBAAiB,CAAC;oBACnEK,CAAC,CAACE,MAAM,CAAC+G,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC/BlH,CAAC,CAACE,MAAM,CAACiH,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,OAAO;kBAC9C,CAAE;kBACFE,MAAM,EAAEA,CAAA,KAAM;oBACZlF,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAExC,iBAAiB,CAAC;kBACxE;gBAAE;kBAAAgG,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5H,OAAA;kBACEsH,SAAS,EAAC,0CAA0C;kBACpDmB,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EAC5B;gBAED;kBAAAJ,QAAA,EAAAO,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAT,QAAA,EAAAO,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAT,QAAA,EAAAO,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAT,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAT,QAAA,EAAAO,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxH,EAAA,CAnXID,IAAI;AAAA0I,EAAA,GAAJ1I,IAAI;AAqXV,eAAeA,IAAI;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}