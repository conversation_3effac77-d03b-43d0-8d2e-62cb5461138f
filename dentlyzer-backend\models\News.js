const mongoose = require('mongoose');

const NewsSchema = new mongoose.Schema(
  {
    title: {
      en: { type: String, required: true },
      ar: { type: String, required: true }
    },
    content: {
      en: { type: String, required: true },
      ar: { type: String, required: true }
    },
    university: { type: String, default: null }, // null for global news
    isGlobal: { type: Boolean, default: false },
    recipientType: {
      type: String,
      enum: ['account', 'university', 'clinic'],
      default: null
    },
    recipientId: { type: String, default: null },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: false },
  },
  { timestamps: true }
);

module.exports = mongoose.model('News', NewsSchema);