const mongoose = require('mongoose');

const upperTeethNumbers = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28];
const lowerTeethNumbers = [48, 47, 46, 45, 44, 43, 42, 41, 31, 32, 33, 34, 35, 36, 37, 38];
const surfaceTypes = ['occlusal', 'incisal', 'buccal', 'mesial', 'palatal', 'distal', 'lingual'];

const ReviewSchema = new mongoose.Schema({
  toothNumber: { type: Number, required: true },
  changes: {
    procedure: { type: String, default: 'N/A' },
    condition: { type: String, default: 'N/A' },
    surfaces: [{ type: String, enum: surfaceTypes }],
    notes: { type: String, default: 'N/A'  }
  },
  timestamp: { type: Date, required: true }
}, { _id: false });

const ToothSchema = new mongoose.Schema({
  toothNumber: { type: Number, enum: [...upperTeethNumbers, ...lowerTeethNumbers], required: true },
  surfaces: [{ type: String, enum: surfaceTypes }],
  procedure: { type: String, required: true },
  condition: { type: String },
  notes: { type: String, maxlength: 500 },
  color: { type: String }
}, { _id: false });

const TeethChartSchema = new mongoose.Schema({
  date: { type: Date, required: true, default: Date.now },
  title: { type: String, required: true, trim: true, maxlength: 100 },
  patient: { type: String, required: true, index: true },
  teeth: [ToothSchema],
  reviews: [ReviewSchema],
  isLocked: { type: Boolean, default: false }
}, { timestamps: true });

TeethChartSchema.index({ patient: 1, date: -1 });
module.exports = mongoose.model('TeethChart', TeethChartSchema);