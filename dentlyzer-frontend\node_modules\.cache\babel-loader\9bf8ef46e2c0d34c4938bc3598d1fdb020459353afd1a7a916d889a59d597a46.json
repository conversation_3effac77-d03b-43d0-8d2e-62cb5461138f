{"ast": null, "code": "var _jsxFileName = \"D:\\\\New folder (3)\\\\dentlyzer-frontend\\\\src\\\\dentist\\\\XRay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport DentistSidebar from './DentistSidebar';\nimport Navbar from './Navbar';\nimport { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot } from 'react-icons/fa';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XRay = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [aiResult, setAiResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [enhancing, setEnhancing] = useState(false);\n  const [error, setError] = useState('');\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    setEnhancedImageUrl(null);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setPreviewUrl(reader.result);\n      reader.readAsDataURL(file);\n    } else {\n      setPreviewUrl(null);\n    }\n  };\n  const handleAnalyze = async () => {\n    if (!selectedFile) return;\n    setLoading(true);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.results) {\n        setAiResult({\n          findings: response.data.results.map(r => ({\n            label: r.class,\n            confidence: r.confidence\n          })),\n          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`\n        });\n        if (response.data.annotatedImagePath) {\n          // Extract filename from the full path\n          const filename = response.data.annotatedImagePath.split('/').pop();\n          setAnnotatedImageUrl(`/api/xray/annotated-image/${filename}`);\n        }\n      } else {\n        setError('No results from AI analysis.');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Failed to analyze X-ray image.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnhance = async () => {\n    if (!selectedFile) return;\n    setEnhancing(true);\n    setError('');\n    setEnhancedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data && response.data.enhancedImage) {\n        setEnhancedImageUrl(response.data.enhancedImage);\n      } else {\n        setError('No enhanced image received from server.');\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || 'Failed to enhance X-ray image.');\n    } finally {\n      setEnhancing(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(DentistSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-blue-900 mb-8\",\n            children: \"X-Ray Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-md p-8 flex flex-col items-center gap-6 border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex flex-col items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                className: \"text-blue-600 text-3xl mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-700 font-medium mb-2\",\n                children: \"Upload X-Ray Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                onChange: handleFileChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n              src: previewUrl,\n              alt: \"X-Ray Preview\",\n              className: \"w-full max-w-xs rounded-lg border border-gray-200 shadow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-4 mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\",\n                onClick: handleEnhance,\n                disabled: !selectedFile || enhancing,\n                children: [/*#__PURE__*/_jsxDEV(FaMagic, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), \" \", enhancing ? 'Enhancing...' : 'Enhance X-Ray']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\",\n                onClick: handleAnalyze,\n                disabled: !selectedFile || loading,\n                children: [/*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this), \" \", loading ? 'Analyzing...' : 'AI Analysis']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full mt-4 bg-red-50 rounded-lg p-4 border border-red-200 text-red-700 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), \" \", error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), enhancedImageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full mt-6 bg-green-50 rounded-lg p-4 border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-bold text-green-800 mb-2 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaMagic, {\n                  className: \"mr-2 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this), \"Enhanced X-Ray Image\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-gray-700 mb-2\",\n                    children: \"Original:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: previewUrl,\n                    alt: \"Original X-Ray\",\n                    className: \"w-full max-w-sm rounded-lg border border-gray-300 shadow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-semibold text-green-700 mb-2\",\n                    children: \"Enhanced:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: enhancedImageUrl,\n                    alt: \"Enhanced X-Ray\",\n                    className: \"w-full max-w-sm rounded-lg border border-green-300 shadow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 text-green-900 font-medium\",\n                children: \"\\u2705 X-ray image has been enhanced with improved contrast, reduced noise, and better visibility of dental structures.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), aiResult && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full mt-6 bg-blue-50 rounded-lg p-4 border border-blue-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-bold text-blue-800 mb-2 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n                  className: \"mr-2 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), \"AI Analysis Results\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4 p-3 bg-blue-100 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-700 font-medium mb-2\",\n                  children: \"\\uD83E\\uDD16 AI Model: YOLOv8 trained on dental X-ray dataset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-600\",\n                  children: \"Can detect: Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth, Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures, Orthodontic brackets, and 18+ other dental conditions.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), aiResult.findings.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-blue-800 mb-2\",\n                  children: \"Detected Conditions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"mb-3 space-y-1\",\n                  children: aiResult.findings.map((f, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"text-gray-700 flex items-center justify-between bg-white p-2 rounded border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-blue-700\",\n                      children: f.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm bg-blue-100 px-2 py-1 rounded\",\n                      children: [(f.confidence * 100).toFixed(1), \"% confidence\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 29\n                    }, this)]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-900 font-medium bg-blue-100 p-2 rounded\",\n                  children: [\"\\uD83D\\uDCCA \", aiResult.summary]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-green-700 bg-green-100 p-3 rounded-lg\",\n                children: \"\\u2705 No significant dental pathology detected in this X-ray image.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this), annotatedImageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-blue-700 mb-2\",\n                  children: \"Annotated X-Ray Image:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: annotatedImageUrl,\n                  alt: \"Annotated X-Ray\",\n                  className: \"w-full max-w-md rounded-lg border border-blue-300 shadow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(XRay, \"LggJ7dspVJn4gPaqFJYM5cPlyBU=\");\n_c = XRay;\nexport default XRay;\nvar _c;\n$RefreshReg$(_c, \"XRay\");", "map": {"version": 3, "names": ["React", "useState", "DentistSidebar", "<PERSON><PERSON><PERSON>", "FaUpload", "FaSearch", "FaCheckCircle", "FaExclamationTriangle", "FaMagic", "FaRobot", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XRay", "_s", "sidebarOpen", "setSidebarOpen", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "aiResult", "setAiResult", "loading", "setLoading", "enhancing", "setEnhancing", "error", "setError", "annotatedImageUrl", "setAnnotatedImageUrl", "enhancedImageUrl", "setEnhancedImageUrl", "handleFileChange", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleAnalyze", "formData", "FormData", "append", "response", "post", "process", "env", "REACT_APP_API_URL", "headers", "data", "results", "findings", "map", "r", "label", "class", "confidence", "summary", "join", "annotatedImagePath", "filename", "split", "pop", "err", "_err$response", "_err$response$data", "handleEnhance", "enhancedImage", "_err$response2", "_err$response2$data", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "type", "accept", "onChange", "src", "alt", "onClick", "disabled", "length", "f", "idx", "toFixed", "_c", "$RefreshReg$"], "sources": ["D:/New folder (3)/dentlyzer-frontend/src/dentist/XRay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport DentistSidebar from './DentistSidebar';\nimport Navbar from './Navbar';\nimport { FaUpload, FaSearch, FaCheckCircle, FaExclamationTriangle, FaMagic, FaRobot } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst XRay = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [aiResult, setAiResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [enhancing, setEnhancing] = useState(false);\n  const [error, setError] = useState('');\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [enhancedImageUrl, setEnhancedImageUrl] = useState(null);\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    setEnhancedImageUrl(null);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setPreviewUrl(reader.result);\n      reader.readAsDataURL(file);\n    } else {\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleAnalyze = async () => {\n    if (!selectedFile) return;\n    setLoading(true);\n    setAiResult(null);\n    setError('');\n    setAnnotatedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/analyze`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' },\n      });\n      if (response.data && response.data.results) {\n        setAiResult({\n          findings: response.data.results.map(r => ({ label: r.class, confidence: r.confidence })),\n          summary: `AI detected: ${response.data.results.map(r => r.class).join(', ')}`\n        });\n        if (response.data.annotatedImagePath) {\n          // Extract filename from the full path\n          const filename = response.data.annotatedImagePath.split('/').pop();\n          setAnnotatedImageUrl(`/api/xray/annotated-image/${filename}`);\n        }\n      } else {\n        setError('No results from AI analysis.');\n      }\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to analyze X-ray image.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEnhance = async () => {\n    if (!selectedFile) return;\n    setEnhancing(true);\n    setError('');\n    setEnhancedImageUrl(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedFile);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/xray/enhance`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' },\n      });\n      if (response.data && response.data.enhancedImage) {\n        setEnhancedImageUrl(response.data.enhancedImage);\n      } else {\n        setError('No enhanced image received from server.');\n      }\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to enhance X-ray image.');\n    } finally {\n      setEnhancing(false);\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <DentistSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <main className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h1 className=\"text-3xl font-bold text-blue-900 mb-8\">X-Ray Analysis</h1>\n            <div className=\"bg-white rounded-xl shadow-md p-8 flex flex-col items-center gap-6 border border-blue-100\">\n              <label className=\"flex flex-col items-center cursor-pointer\">\n                <FaUpload className=\"text-blue-600 text-3xl mb-2\" />\n                <span className=\"text-blue-700 font-medium mb-2\">Upload X-Ray Image</span>\n                <input type=\"file\" accept=\"image/*\" className=\"hidden\" onChange={handleFileChange} />\n              </label>\n              {previewUrl && (\n                <img src={previewUrl} alt=\"X-Ray Preview\" className=\"w-full max-w-xs rounded-lg border border-gray-200 shadow\" />\n              )}\n              <div className=\"flex gap-4 mt-4\">\n                <button\n                  className=\"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\"\n                  onClick={handleEnhance}\n                  disabled={!selectedFile || enhancing}\n                >\n                  <FaMagic /> {enhancing ? 'Enhancing...' : 'Enhance X-Ray'}\n                </button>\n                <button\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 disabled:opacity-50\"\n                  onClick={handleAnalyze}\n                  disabled={!selectedFile || loading}\n                >\n                  <FaRobot /> {loading ? 'Analyzing...' : 'AI Analysis'}\n                </button>\n              </div>\n              {error && (\n                <div className=\"w-full mt-4 bg-red-50 rounded-lg p-4 border border-red-200 text-red-700 flex items-center gap-2\">\n                  <FaExclamationTriangle /> {error}\n                </div>\n              )}\n              {enhancedImageUrl && (\n                <div className=\"w-full mt-6 bg-green-50 rounded-lg p-4 border border-green-200\">\n                  <h2 className=\"text-lg font-bold text-green-800 mb-2 flex items-center\">\n                    <FaMagic className=\"mr-2 text-green-500\" />Enhanced X-Ray Image\n                  </h2>\n                  <div className=\"flex flex-col md:flex-row gap-4\">\n                    <div className=\"flex-1\">\n                      <div className=\"font-semibold text-gray-700 mb-2\">Original:</div>\n                      <img src={previewUrl} alt=\"Original X-Ray\" className=\"w-full max-w-sm rounded-lg border border-gray-300 shadow\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"font-semibold text-green-700 mb-2\">Enhanced:</div>\n                      <img src={enhancedImageUrl} alt=\"Enhanced X-Ray\" className=\"w-full max-w-sm rounded-lg border border-green-300 shadow\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-3 text-green-900 font-medium\">\n                    ✅ X-ray image has been enhanced with improved contrast, reduced noise, and better visibility of dental structures.\n                  </div>\n                </div>\n              )}\n              {aiResult && (\n                <div className=\"w-full mt-6 bg-blue-50 rounded-lg p-4 border border-blue-200\">\n                  <h2 className=\"text-lg font-bold text-blue-800 mb-2 flex items-center\">\n                    <FaRobot className=\"mr-2 text-blue-500\" />AI Analysis Results\n                  </h2>\n                  <div className=\"mb-4 p-3 bg-blue-100 rounded-lg\">\n                    <div className=\"text-sm text-blue-700 font-medium mb-2\">\n                      🤖 AI Model: YOLOv8 trained on dental X-ray dataset\n                    </div>\n                    <div className=\"text-xs text-blue-600\">\n                      Can detect: Caries, Crown, Filling, Implant, Malaligned teeth, Missing teeth,\n                      Periapical lesions, Root canal treatment, Impacted teeth, Bone loss, Fractures,\n                      Orthodontic brackets, and 18+ other dental conditions.\n                    </div>\n                  </div>\n                  {aiResult.findings.length > 0 ? (\n                    <>\n                      <h3 className=\"font-semibold text-blue-800 mb-2\">Detected Conditions:</h3>\n                      <ul className=\"mb-3 space-y-1\">\n                        {aiResult.findings.map((f, idx) => (\n                          <li key={idx} className=\"text-gray-700 flex items-center justify-between bg-white p-2 rounded border\">\n                            <span className=\"font-semibold text-blue-700\">{f.label}</span>\n                            <span className=\"text-sm bg-blue-100 px-2 py-1 rounded\">\n                              {(f.confidence * 100).toFixed(1)}% confidence\n                            </span>\n                          </li>\n                        ))}\n                      </ul>\n                      <div className=\"text-blue-900 font-medium bg-blue-100 p-2 rounded\">\n                        📊 {aiResult.summary}\n                      </div>\n                    </>\n                  ) : (\n                    <div className=\"text-green-700 bg-green-100 p-3 rounded-lg\">\n                      ✅ No significant dental pathology detected in this X-ray image.\n                    </div>\n                  )}\n                  {annotatedImageUrl && (\n                    <div className=\"mt-4\">\n                      <div className=\"font-semibold text-blue-700 mb-2\">Annotated X-Ray Image:</div>\n                      <img src={annotatedImageUrl} alt=\"Annotated X-Ray\" className=\"w-full max-w-md rounded-lg border border-blue-300 shadow\" />\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default XRay;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC3G,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMkC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BnB,eAAe,CAACiB,IAAI,CAAC;IACrBb,WAAW,CAAC,IAAI,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;IACZE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAIG,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMpB,aAAa,CAACkB,MAAM,CAACG,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLf,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC1B,YAAY,EAAE;IACnBO,UAAU,CAAC,IAAI,CAAC;IAChBF,WAAW,CAAC,IAAI,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;IACZE,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAMc,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7B,YAAY,CAAC;MACtC,MAAM8B,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,QAAQ,EAAE;QAC/FQ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACC,OAAO,EAAE;QAC1ChC,WAAW,CAAC;UACViC,QAAQ,EAAER,QAAQ,CAACM,IAAI,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,KAAK;YAAEC,KAAK,EAAED,CAAC,CAACE,KAAK;YAAEC,UAAU,EAAEH,CAAC,CAACG;UAAW,CAAC,CAAC,CAAC;UACxFC,OAAO,EAAE,gBAAgBd,QAAQ,CAACM,IAAI,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,KAAK,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;QAC7E,CAAC,CAAC;QACF,IAAIf,QAAQ,CAACM,IAAI,CAACU,kBAAkB,EAAE;UACpC;UACA,MAAMC,QAAQ,GAAGjB,QAAQ,CAACM,IAAI,CAACU,kBAAkB,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UAClEpC,oBAAoB,CAAC,6BAA6BkC,QAAQ,EAAE,CAAC;QAC/D;MACF,CAAC,MAAM;QACLpC,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOuC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZzC,QAAQ,CAAC,EAAAwC,aAAA,GAAAD,GAAG,CAACpB,QAAQ,cAAAqB,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcf,IAAI,cAAAgB,kBAAA,uBAAlBA,kBAAA,CAAoB1C,KAAK,KAAI,gCAAgC,CAAC;IACzE,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACrD,YAAY,EAAE;IACnBS,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZI,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMY,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE7B,YAAY,CAAC;MACtC,MAAM8B,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,IAAI,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,QAAQ,EAAE;QAC/FQ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACkB,aAAa,EAAE;QAChDvC,mBAAmB,CAACe,QAAQ,CAACM,IAAI,CAACkB,aAAa,CAAC;MAClD,CAAC,MAAM;QACL3C,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOuC,GAAG,EAAE;MAAA,IAAAK,cAAA,EAAAC,mBAAA;MACZ7C,QAAQ,CAAC,EAAA4C,cAAA,GAAAL,GAAG,CAACpB,QAAQ,cAAAyB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoB9C,KAAK,KAAI,gCAAgC,CAAC;IACzE,CAAC,SAAS;MACRD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKgE,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCjE,OAAA,CAACV,cAAc;MAAC4E,MAAM,EAAE7D,WAAY;MAAC8D,SAAS,EAAE7D;IAAe;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClEvE,OAAA;MAAKgE,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDjE,OAAA,CAACT,MAAM;QAACiF,aAAa,EAAEA,CAAA,KAAMlE,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DvE,OAAA;QAAMgE,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eAClFjE,OAAA;UAAKgE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjE,OAAA;YAAIgE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEvE,OAAA;YAAKgE,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBACxGjE,OAAA;cAAOgE,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBAC1DjE,OAAA,CAACR,QAAQ;gBAACwE,SAAS,EAAC;cAA6B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDvE,OAAA;gBAAMgE,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1EvE,OAAA;gBAAOyE,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,SAAS;gBAACV,SAAS,EAAC,QAAQ;gBAACW,QAAQ,EAAEpD;cAAiB;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACP9D,UAAU,iBACTT,OAAA;cAAK4E,GAAG,EAAEnE,UAAW;cAACoE,GAAG,EAAC,eAAe;cAACb,SAAS,EAAC;YAA0D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACjH,eACDvE,OAAA;cAAKgE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BjE,OAAA;gBACEgE,SAAS,EAAC,2HAA2H;gBACrIc,OAAO,EAAElB,aAAc;gBACvBmB,QAAQ,EAAE,CAACxE,YAAY,IAAIQ,SAAU;gBAAAkD,QAAA,gBAErCjE,OAAA,CAACJ,OAAO;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxD,SAAS,GAAG,cAAc,GAAG,eAAe;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACTvE,OAAA;gBACEgE,SAAS,EAAC,yHAAyH;gBACnIc,OAAO,EAAE7C,aAAc;gBACvB8C,QAAQ,EAAE,CAACxE,YAAY,IAAIM,OAAQ;gBAAAoD,QAAA,gBAEnCjE,OAAA,CAACH,OAAO;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC1D,OAAO,GAAG,cAAc,GAAG,aAAa;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLtD,KAAK,iBACJjB,OAAA;cAAKgE,SAAS,EAAC,iGAAiG;cAAAC,QAAA,gBAC9GjE,OAAA,CAACL,qBAAqB;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACtD,KAAK;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CACN,EACAlD,gBAAgB,iBACfrB,OAAA;cAAKgE,SAAS,EAAC,gEAAgE;cAAAC,QAAA,gBAC7EjE,OAAA;gBAAIgE,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBACrEjE,OAAA,CAACJ,OAAO;kBAACoE,SAAS,EAAC;gBAAqB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAC7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBAAKgE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CjE,OAAA;kBAAKgE,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBjE,OAAA;oBAAKgE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjEvE,OAAA;oBAAK4E,GAAG,EAAEnE,UAAW;oBAACoE,GAAG,EAAC,gBAAgB;oBAACb,SAAS,EAAC;kBAA0D;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC,eACNvE,OAAA;kBAAKgE,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBjE,OAAA;oBAAKgE,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClEvE,OAAA;oBAAK4E,GAAG,EAAEvD,gBAAiB;oBAACwD,GAAG,EAAC,gBAAgB;oBAACb,SAAS,EAAC;kBAA2D;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvE,OAAA;gBAAKgE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EACA5D,QAAQ,iBACPX,OAAA;cAAKgE,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3EjE,OAAA;gBAAIgE,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACpEjE,OAAA,CAACH,OAAO;kBAACmE,SAAS,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAC5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBAAKgE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CjE,OAAA;kBAAKgE,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAExD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvE,OAAA;kBAAKgE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAIvC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL5D,QAAQ,CAACkC,QAAQ,CAACmC,MAAM,GAAG,CAAC,gBAC3BhF,OAAA,CAAAE,SAAA;gBAAA+D,QAAA,gBACEjE,OAAA;kBAAIgE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1EvE,OAAA;kBAAIgE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAC3BtD,QAAQ,CAACkC,QAAQ,CAACC,GAAG,CAAC,CAACmC,CAAC,EAAEC,GAAG,kBAC5BlF,OAAA;oBAAcgE,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,gBACnGjE,OAAA;sBAAMgE,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAEgB,CAAC,CAACjC;oBAAK;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC9DvE,OAAA;sBAAMgE,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,GACpD,CAACgB,CAAC,CAAC/B,UAAU,GAAG,GAAG,EAAEiC,OAAO,CAAC,CAAC,CAAC,EAAC,cACnC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GAJAW,GAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKR,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLvE,OAAA;kBAAKgE,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,eAC9D,EAACtD,QAAQ,CAACwC,OAAO;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA,eACN,CAAC,gBAEHvE,OAAA;gBAAKgE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAE5D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACApD,iBAAiB,iBAChBnB,OAAA;gBAAKgE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjE,OAAA;kBAAKgE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9EvE,OAAA;kBAAK4E,GAAG,EAAEzD,iBAAkB;kBAAC0D,GAAG,EAAC,iBAAiB;kBAACb,SAAS,EAAC;gBAA0D;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CA/LID,IAAI;AAAAiF,EAAA,GAAJjF,IAAI;AAiMV,eAAeA,IAAI;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}