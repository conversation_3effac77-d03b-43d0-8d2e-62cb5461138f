const Joi = require('joi');
const ProcedureRequest = require('../models/ProcedureRequest');
const Patient = require('../models/Patient');
const Student = require('../models/Student');

// Validation schema for creating a procedure request
const procedureRequestSchema = Joi.object({
  procedureType: Joi.string().valid(
    'Periodontics',
    'Endodontics',
    'Oral Surgery',
    'Fixed Prosthodontics',
    'Removable Prosthodontics',
    'Operative'
  ).required(),
  patientNationalId: Joi.string().allow(''),
  patientName: Joi.string().allow(''),
  notes: Joi.string().allow(''),
});

// Validation schema for updating a procedure request
const updateProcedureRequestSchema = Joi.object({
  status: Joi.string().valid('approved', 'rejected').required(),
  responseNotes: Joi.string().allow(''),
});

// Create a new procedure request
const createProcedureRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = procedureRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { procedureType, patientNationalId, patientName, notes } = req.body;

    // Find the student
    const student = await Student.findOne({ studentId: req.user.studentId });
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // If patientNationalId is provided, verify the patient exists
    let patientFullName = patientName || '';
    if (patientNationalId) {
      const patient = await Patient.findOne({ nationalId: patientNationalId });
      if (patient) {
        patientFullName = patient.fullName;
      }
    }

    // Create the procedure request
    const procedureRequest = new ProcedureRequest({
      studentId: req.user.studentId,
      studentName: req.user.name,
      procedureType,
      patientNationalId: patientNationalId || '',
      patientName: patientFullName,
      notes: notes || '',
    });

    await procedureRequest.save();

    res.status(201).json({
      message: 'Procedure request created successfully',
      procedureRequest
    });
  } catch (error) {
    console.error('Error creating procedure request:', error);
    res.status(500).json({
      message: 'Server error while creating procedure request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all procedure requests for a student
const getStudentProcedureRequests = async (req, res) => {
  try {
    const procedureRequests = await ProcedureRequest.find({ studentId: req.user.studentId })
      .sort({ requestDate: -1 });

    res.json(procedureRequests);
  } catch (error) {
    console.error('Error fetching student procedure requests:', error);
    res.status(500).json({
      message: 'Server error while fetching procedure requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all procedure requests (for assistants)
const getAllProcedureRequests = async (req, res) => {
  try {
    const procedureRequests = await ProcedureRequest.find()
      .sort({ requestDate: -1 });

    res.json(procedureRequests);
  } catch (error) {
    console.error('Error fetching all procedure requests:', error);
    res.status(500).json({
      message: 'Server error while fetching procedure requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update a procedure request (approve/reject)
const updateProcedureRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = updateProcedureRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { status, responseNotes } = req.body;
    const { requestId } = req.params;

    const procedureRequest = await ProcedureRequest.findById(requestId);
    if (!procedureRequest) {
      return res.status(404).json({ message: 'Procedure request not found' });
    }

    // Update the procedure request
    procedureRequest.status = status;
    procedureRequest.responseNotes = responseNotes || '';
    procedureRequest.responseDate = new Date();
    procedureRequest.responderId = req.user.id;
    procedureRequest.responderName = req.user.name;

    await procedureRequest.save();

    res.json({
      message: `Procedure request ${status}`,
      procedureRequest
    });
  } catch (error) {
    console.error('Error updating procedure request:', error);
    res.status(500).json({
      message: 'Server error while updating procedure request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createProcedureRequest,
  getStudentProcedureRequests,
  getAllProcedureRequests,
  updateProcedureRequest
};
