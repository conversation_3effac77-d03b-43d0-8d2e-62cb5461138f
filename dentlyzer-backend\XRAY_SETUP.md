# X-Ray Analysis & Enhancement Setup Guide

This guide explains how to set up and use the X-Ray analysis and enhancement features using YOLOv8 and AI enhancement.

## New Features

### 1. X-Ray Enhancement
- **AI-powered image enhancement** using advanced computer vision techniques
- **Improves image quality** with denoising, contrast enhancement, and edge sharpening
- **Optimized for dental X-rays** with specific algorithms for dental structures

### 2. Comprehensive AI Analysis
- **YOLOv8 model** trained specifically on dental X-ray dataset
- **31 different dental conditions** can be detected including:
  - Caries, Crown, Filling, Implant, Malaligned teeth
  - Missing teeth, Periapical lesions, Root canal treatment
  - Impacted teeth, Bone loss, Fractures, Orthodontic brackets
  - And 18+ other dental conditions
- **High accuracy detection** with confidence scores
- **Visual annotations** with bounding boxes and labels

## Prerequisites

1. **Python Dependencies**: Install the required Python packages for YOLO detection
2. **X-Ray Model**: Ensure the `xray.pt` model file is present in the backend directory
3. **Node.js Backend**: The Express.js backend should be running

## Installation Steps

### 1. Install Python Dependencies

Navigate to the backend directory and install the required Python packages:

```bash
cd dentlyzer-backend
pip install -r requirements.txt
```

Or install manually:

```bash
pip install ultralytics opencv-python numpy torch torchvision Pillow
```

### 2. Verify X-Ray Model

Run the test script to verify the X-ray model is working:

```bash
python test_xray.py
```

You should see output like:
```
🧪 Testing X-ray YOLO Model
========================================
✅ X-ray model found: xray.pt
📁 Model size: 351.2 MB
🔄 Loading X-ray model...
✅ X-ray model loaded successfully!
📊 Model classes: {...}
🔍 Number of classes: X

✅ X-ray model test passed!
🚀 Ready for X-ray analysis
```

### 3. Start the Backend Server

```bash
npm start
# or for development
npm run dev
```

## API Endpoints

### X-Ray Enhancement
- **POST** `/api/xray/enhance`
  - Upload an X-ray image for AI enhancement
  - Returns enhanced image as base64 data
  - Improves contrast, reduces noise, sharpens edges

### X-Ray Analysis
- **POST** `/api/xray/analyze`
  - Upload an X-ray image for AI analysis
  - Returns detection results and annotated image path
  - Detects 31 different dental conditions

### Annotated Image
- **GET** `/api/xray/annotated-image/:filename`
  - Serve the annotated image with bounding boxes

### Health Check
- **GET** `/api/xray/health`
  - Check if the X-ray model is available

## Frontend Integration

The frontend X-Ray component (`dentlyzer-frontend/src/dentist/XRay.jsx`) now includes:

### Enhancement Features:
1. **Enhance X-Ray button** - Improves image quality using AI
2. **Side-by-side comparison** - Shows original vs enhanced images
3. **Real-time processing** - Enhanced images displayed immediately

### Analysis Features:
1. **AI Analysis button** - Detects dental conditions using YOLOv8
2. **Comprehensive results** - Lists all detected conditions with confidence scores
3. **Visual annotations** - Shows annotated image with bounding boxes
4. **Detailed information** - Explains what the AI can detect

## Usage Flow

1. **Dentist uploads X-ray image** through the frontend interface
2. **Backend processes the image** using the YOLOv8 `xray.pt` model
3. **Detection results are returned** with class names and confidence scores
4. **Annotated image is generated** with bounding boxes and labels
5. **Frontend displays results** showing both text results and visual annotations

## Model Information

- **Model File**: `xray.pt` (YOLOv8 format)
- **Model Size**: ~351 MB
- **Detection Classes**: X-ray specific classes (determined by the model)
- **Output**: Bounding boxes with class labels and confidence scores

## Troubleshooting

### Common Issues

1. **Model not found**: Ensure `xray.pt` is in the backend directory
2. **Python dependencies missing**: Run `pip install -r requirements.txt`
3. **CUDA/GPU issues**: The model will run on CPU if GPU is not available
4. **Memory issues**: Ensure sufficient RAM for model loading (~1GB recommended)

### Testing

Use the test scripts to verify setup:

**Test X-ray Analysis:**
```bash
python test_xray.py
```

**Test X-ray Enhancement:**
```bash
python test_enhance.py
```

Both tests should pass for full functionality.

### Logs

Check the backend console for detailed logs during analysis:
- Model loading status
- Detection results
- Error messages

## Security Notes

- Uploaded images are temporarily stored and automatically cleaned up
- Annotated images are saved in the `uploads/` directory
- Consider implementing authentication for production use 